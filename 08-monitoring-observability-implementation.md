# Monitoring & Observability - Implementation Plan

## Overview
The Monitoring & Observability Module provides comprehensive system visibility through structured logging, metrics collection, performance monitoring, and alerting. It uses Grafana, Loki, and Prometheus to create a complete observability stack for the trend platform.

## Dependencies
- **Infrastructure Module**: Monitoring infrastructure and data storage
- **All System Modules**: Metrics and log data sources
- **Security Module**: Secure access to monitoring endpoints
- **Database Module**: Performance metrics and query monitoring

## Interfaces
### Outbound
- **Grafana**: Dashboard visualization and alerting
- **Loki**: Log aggregation and storage
- **Prometheus**: Metrics collection and storage
- **Alert Manager**: Notification routing and management

### Inbound
- **All Modules**: Log entries and metrics data
- **Infrastructure**: System-level metrics (CPU, memory, disk)
- **Applications**: Application performance metrics
- **External Services**: Third-party service monitoring

## Implementation Phases

### Phase 1: Logging Infrastructure
**Duration**: Week 8-9

#### 1.1 Structured Logging System
```python
# monitoring/loggers/structured_logger.py
import json
import logging
import structlog
from datetime import datetime
from typing import Dict, Any, Optional
from enum import Enum

class LogLevel(str, Enum):
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class StructuredLogger:
    def __init__(self, module_name: str, service_name: str = "trend-platform"):
        self.module_name = module_name
        self.service_name = service_name
        self.logger = self._setup_logger()
    
    def _setup_logger(self) -> structlog.BoundLogger:
        """Configure structured logging with JSON output"""
        structlog.configure(
            processors=[
                structlog.stdlib.filter_by_level,
                structlog.stdlib.add_logger_name,
                structlog.stdlib.add_log_level,
                structlog.stdlib.PositionalArgumentsFormatter(),
                structlog.processors.TimeStamper(fmt="iso"),
                structlog.processors.StackInfoRenderer(),
                structlog.processors.format_exc_info,
                structlog.processors.UnicodeDecoder(),
                structlog.processors.JSONRenderer()
            ],
            context_class=dict,
            logger_factory=structlog.stdlib.LoggerFactory(),
            wrapper_class=structlog.stdlib.BoundLogger,
            cache_logger_on_first_use=True,
        )
        
        return structlog.get_logger(self.module_name)
    
    def log(
        self, 
        level: LogLevel, 
        message: str, 
        **kwargs
    ):
        """Log structured message with metadata"""
        log_data = {
            'service': self.service_name,
            'module': self.module_name,
            'timestamp': datetime.utcnow().isoformat(),
            'message': message,
            **kwargs
        }
        
        getattr(self.logger, level.value)(**log_data)
    
    def info(self, message: str, **kwargs):
        self.log(LogLevel.INFO, message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        self.log(LogLevel.WARNING, message, **kwargs)
    
    def error(self, message: str, **kwargs):
        self.log(LogLevel.ERROR, message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        self.log(LogLevel.CRITICAL, message, **kwargs)
    
    def debug(self, message: str, **kwargs):
        self.log(LogLevel.DEBUG, message, **kwargs)

# Usage example in modules
class TrendScraperLogger:
    def __init__(self):
        self.logger = StructuredLogger('trend_scraper')
    
    def log_scraping_start(self, source: str, region: str, category: str):
        self.logger.info(
            "Starting trend scraping",
            source=source,
            region=region,
            category=category,
            operation="scrape_start"
        )
    
    def log_scraping_complete(self, source: str, trends_found: int, duration: float):
        self.logger.info(
            "Trend scraping completed",
            source=source,
            trends_found=trends_found,
            duration_seconds=duration,
            operation="scrape_complete"
        )
    
    def log_scraping_error(self, source: str, error: str, **context):
        self.logger.error(
            "Trend scraping failed",
            source=source,
            error=error,
            operation="scrape_error",
            **context
        )
```

#### 1.2 Log Aggregation System
```python
# monitoring/loggers/log_aggregator.py
import asyncio
import aiohttp
from typing import Dict, Any, List
from datetime import datetime
import json

class LokiLogAggregator:
    def __init__(self, loki_url: str, auth_token: str = None):
        self.loki_url = loki_url.rstrip('/')
        self.auth_token = auth_token
        self.session = None
        self.log_buffer = []
        self.buffer_size = 100
        self.flush_interval = 30  # seconds
    
    async def initialize(self):
        """Initialize HTTP session and start background tasks"""
        headers = {'Content-Type': 'application/json'}
        if self.auth_token:
            headers['Authorization'] = f'Bearer {self.auth_token}'
        
        self.session = aiohttp.ClientSession(headers=headers)
        
        # Start background log flushing
        asyncio.create_task(self._flush_logs_periodically())
    
    async def send_log(self, log_entry: Dict[str, Any]):
        """Send single log entry to Loki"""
        # Convert to Loki format
        loki_entry = self._convert_to_loki_format(log_entry)
        
        # Add to buffer
        self.log_buffer.append(loki_entry)
        
        # Flush if buffer is full
        if len(self.log_buffer) >= self.buffer_size:
            await self._flush_logs()
    
    def _convert_to_loki_format(self, log_entry: Dict[str, Any]) -> Dict[str, Any]:
        """Convert log entry to Loki push format"""
        timestamp = log_entry.get('timestamp', datetime.utcnow().isoformat())
        
        # Extract labels
        labels = {
            'service': log_entry.get('service', 'unknown'),
            'module': log_entry.get('module', 'unknown'),
            'level': log_entry.get('level', 'info'),
            'environment': log_entry.get('environment', 'production')
        }
        
        # Create label string
        label_string = ','.join([f'{k}="{v}"' for k, v in labels.items()])
        
        return {
            'stream': {**labels},
            'values': [[
                str(int(datetime.fromisoformat(timestamp.replace('Z', '+00:00')).timestamp() * 1000000000)),
                json.dumps(log_entry)
            ]]
        }
    
    async def _flush_logs(self):
        """Flush buffered logs to Loki"""
        if not self.log_buffer or not self.session:
            return
        
        try:
            # Group logs by stream labels
            streams = {}
            for log_entry in self.log_buffer:
                stream_key = json.dumps(log_entry['stream'], sort_keys=True)
                if stream_key not in streams:
                    streams[stream_key] = {
                        'stream': log_entry['stream'],
                        'values': []
                    }
                streams[stream_key]['values'].extend(log_entry['values'])
            
            # Send to Loki
            payload = {'streams': list(streams.values())}
            
            async with self.session.post(
                f"{self.loki_url}/loki/api/v1/push",
                json=payload
            ) as response:
                if response.status == 204:
                    self.log_buffer.clear()
                else:
                    print(f"Failed to send logs to Loki: {response.status}")
        
        except Exception as e:
            print(f"Error flushing logs to Loki: {str(e)}")
    
    async def _flush_logs_periodically(self):
        """Periodically flush logs to Loki"""
        while True:
            await asyncio.sleep(self.flush_interval)
            await self._flush_logs()
```

### Phase 2: Metrics Collection
**Duration**: Week 9

#### 2.1 Application Metrics
```python
# monitoring/metrics/app_metrics.py
from prometheus_client import Counter, Histogram, Gauge, CollectorRegistry
from typing import Dict, Any
import time
from functools import wraps

class ApplicationMetrics:
    def __init__(self, registry: CollectorRegistry = None):
        self.registry = registry or CollectorRegistry()
        self._setup_metrics()
    
    def _setup_metrics(self):
        """Initialize Prometheus metrics"""
        # Trend scraping metrics
        self.trends_scraped_total = Counter(
            'trends_scraped_total',
            'Total number of trends scraped',
            ['source', 'region', 'category'],
            registry=self.registry
        )
        
        self.scraping_duration = Histogram(
            'scraping_duration_seconds',
            'Time spent scraping trends',
            ['source'],
            registry=self.registry
        )
        
        self.scraping_errors_total = Counter(
            'scraping_errors_total',
            'Total scraping errors',
            ['source', 'error_type'],
            registry=self.registry
        )
        
        # Content generation metrics
        self.content_generated_total = Counter(
            'content_generated_total',
            'Total content pieces generated',
            ['category', 'ai_provider'],
            registry=self.registry
        )
        
        self.content_generation_duration = Histogram(
            'content_generation_duration_seconds',
            'Time spent generating content',
            ['content_type'],
            registry=self.registry
        )
        
        self.ai_api_calls_total = Counter(
            'ai_api_calls_total',
            'Total AI API calls',
            ['provider', 'model', 'call_type'],
            registry=self.registry
        )
        
        # Deployment metrics
        self.deployments_total = Counter(
            'deployments_total',
            'Total deployments',
            ['status'],
            registry=self.registry
        )
        
        self.deployment_duration = Histogram(
            'deployment_duration_seconds',
            'Time spent on deployments',
            registry=self.registry
        )
        
        # System health metrics
        self.active_trends = Gauge(
            'active_trends_total',
            'Number of currently active trends',
            registry=self.registry
        )
        
        self.pending_trends = Gauge(
            'pending_trends_total',
            'Number of pending trends',
            registry=self.registry
        )
        
        # Performance metrics
        self.http_requests_total = Counter(
            'http_requests_total',
            'Total HTTP requests',
            ['method', 'endpoint', 'status'],
            registry=self.registry
        )
        
        self.http_request_duration = Histogram(
            'http_request_duration_seconds',
            'HTTP request duration',
            ['method', 'endpoint'],
            registry=self.registry
        )
    
    def record_trend_scraped(self, source: str, region: str, category: str, count: int = 1):
        """Record trends scraped"""
        self.trends_scraped_total.labels(
            source=source, 
            region=region, 
            category=category
        ).inc(count)
    
    def record_scraping_duration(self, source: str, duration: float):
        """Record scraping duration"""
        self.scraping_duration.labels(source=source).observe(duration)
    
    def record_scraping_error(self, source: str, error_type: str):
        """Record scraping error"""
        self.scraping_errors_total.labels(
            source=source, 
            error_type=error_type
        ).inc()
    
    def record_content_generated(self, category: str, ai_provider: str):
        """Record content generation"""
        self.content_generated_total.labels(
            category=category,
            ai_provider=ai_provider
        ).inc()
    
    def record_deployment(self, status: str, duration: float = None):
        """Record deployment"""
        self.deployments_total.labels(status=status).inc()
        if duration:
            self.deployment_duration.observe(duration)
    
    def update_trend_counts(self, active: int, pending: int):
        """Update trend count gauges"""
        self.active_trends.set(active)
        self.pending_trends.set(pending)
    
    def time_function(self, metric_name: str, labels: Dict[str, str] = None):
        """Decorator to time function execution"""
        def decorator(func):
            @wraps(func)
            async def async_wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = await func(*args, **kwargs)
                    return result
                finally:
                    duration = time.time() - start_time
                    metric = getattr(self, metric_name)
                    if labels:
                        metric.labels(**labels).observe(duration)
                    else:
                        metric.observe(duration)
            
            @wraps(func)
            def sync_wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = func(*args, **kwargs)
                    return result
                finally:
                    duration = time.time() - start_time
                    metric = getattr(self, metric_name)
                    if labels:
                        metric.labels(**labels).observe(duration)
                    else:
                        metric.observe(duration)
            
            return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
        return decorator

# Global metrics instance
app_metrics = ApplicationMetrics()

# Usage examples
@app_metrics.time_function('scraping_duration', {'source': 'google_trends'})
async def scrape_google_trends():
    # Scraping logic here
    pass

def record_api_call(provider: str, model: str, call_type: str):
    app_metrics.ai_api_calls_total.labels(
        provider=provider,
        model=model,
        call_type=call_type
    ).inc()
```

#### 2.2 Infrastructure Metrics
```python
# monitoring/metrics/infra_metrics.py
import psutil
import asyncio
from prometheus_client import Gauge, CollectorRegistry
from typing import Dict, Any

class InfrastructureMetrics:
    def __init__(self, registry: CollectorRegistry = None):
        self.registry = registry or CollectorRegistry()
        self._setup_metrics()
        self.collection_interval = 30  # seconds
    
    def _setup_metrics(self):
        """Initialize infrastructure metrics"""
        # System metrics
        self.cpu_usage_percent = Gauge(
            'cpu_usage_percent',
            'CPU usage percentage',
            registry=self.registry
        )
        
        self.memory_usage_percent = Gauge(
            'memory_usage_percent',
            'Memory usage percentage',
            registry=self.registry
        )
        
        self.memory_usage_bytes = Gauge(
            'memory_usage_bytes',
            'Memory usage in bytes',
            ['type'],
            registry=self.registry
        )
        
        self.disk_usage_percent = Gauge(
            'disk_usage_percent',
            'Disk usage percentage',
            ['device'],
            registry=self.registry
        )
        
        self.disk_usage_bytes = Gauge(
            'disk_usage_bytes',
            'Disk usage in bytes',
            ['device', 'type'],
            registry=self.registry
        )
        
        self.network_bytes_total = Gauge(
            'network_bytes_total',
            'Network bytes transferred',
            ['interface', 'direction'],
            registry=self.registry
        )
        
        # Database metrics
        self.db_connections_active = Gauge(
            'db_connections_active',
            'Active database connections',
            registry=self.registry
        )
        
        self.db_query_duration = Gauge(
            'db_query_duration_seconds',
            'Database query duration',
            ['query_type'],
            registry=self.registry
        )
        
        # Application-specific metrics
        self.celery_queue_length = Gauge(
            'celery_queue_length',
            'Celery queue length',
            ['queue'],
            registry=self.registry
        )
        
        self.redis_memory_usage = Gauge(
            'redis_memory_usage_bytes',
            'Redis memory usage',
            registry=self.registry
        )
    
    async def start_collection(self):
        """Start periodic metrics collection"""
        while True:
            try:
                await self.collect_system_metrics()
                await self.collect_application_metrics()
                await asyncio.sleep(self.collection_interval)
            except Exception as e:
                print(f"Error collecting metrics: {str(e)}")
                await asyncio.sleep(self.collection_interval)
    
    async def collect_system_metrics(self):
        """Collect system-level metrics"""
        # CPU usage
        cpu_percent = psutil.cpu_percent(interval=1)
        self.cpu_usage_percent.set(cpu_percent)
        
        # Memory usage
        memory = psutil.virtual_memory()
        self.memory_usage_percent.set(memory.percent)
        self.memory_usage_bytes.labels(type='used').set(memory.used)
        self.memory_usage_bytes.labels(type='available').set(memory.available)
        self.memory_usage_bytes.labels(type='total').set(memory.total)
        
        # Disk usage
        for partition in psutil.disk_partitions():
            try:
                usage = psutil.disk_usage(partition.mountpoint)
                device = partition.device.replace('/', '_')
                
                self.disk_usage_percent.labels(device=device).set(
                    (usage.used / usage.total) * 100
                )
                self.disk_usage_bytes.labels(device=device, type='used').set(usage.used)
                self.disk_usage_bytes.labels(device=device, type='free').set(usage.free)
                self.disk_usage_bytes.labels(device=device, type='total').set(usage.total)
            except PermissionError:
                continue
        
        # Network usage
        network = psutil.net_io_counters(pernic=True)
        for interface, stats in network.items():
            self.network_bytes_total.labels(
                interface=interface, 
                direction='sent'
            ).set(stats.bytes_sent)
            self.network_bytes_total.labels(
                interface=interface, 
                direction='recv'
            ).set(stats.bytes_recv)
    
    async def collect_application_metrics(self):
        """Collect application-specific metrics"""
        # Database connection pool metrics
        # This would integrate with your database connection pool
        # active_connections = await get_active_db_connections()
        # self.db_connections_active.set(active_connections)
        
        # Celery queue metrics
        # queue_lengths = await get_celery_queue_lengths()
        # for queue, length in queue_lengths.items():
        #     self.celery_queue_length.labels(queue=queue).set(length)
        
        # Redis metrics
        # redis_info = await get_redis_info()
        # self.redis_memory_usage.set(redis_info.get('used_memory', 0))
        pass

# Global infrastructure metrics instance
infra_metrics = InfrastructureMetrics()
```

## Key Components

### Directory Structure
```
monitoring/
├── __init__.py
├── config.py            # Monitoring configuration
├── loggers/
│   ├── __init__.py
│   ├── structured_logger.py # JSON logging formatter
│   └── log_aggregator.py    # Log collection and forwarding
├── metrics/
│   ├── __init__.py
│   ├── app_metrics.py       # Application-level metrics
│   ├── infra_metrics.py     # Infrastructure metrics
│   └── custom_metrics.py    # Custom business metrics
├── dashboards/
│   ├── grafana/
│   │   ├── system-overview.json
│   │   ├── application-metrics.json
│   │   └── business-metrics.json
│   └── templates/
├── alerts/
│   ├── alert_rules.yml     # Prometheus alert rules
│   ├── alert_manager.py    # Alert management
│   └── notification_handlers.py
├── exporters/
│   ├── prometheus_exporter.py # Metrics export endpoint
│   └── custom_exporters.py
└── utils/
    ├── health_checker.py   # Health check utilities
    ├── performance_profiler.py # Performance profiling
    └── trace_collector.py  # Distributed tracing
```

### Configuration
```python
# monitoring/config.py
MONITORING_CONFIG = {
    'logging': {
        'level': 'INFO',
        'format': 'json',
        'aggregation': {
            'enabled': True,
            'loki_url': env('LOKI_URL'),
            'buffer_size': 100,
            'flush_interval': 30
        }
    },
    'metrics': {
        'enabled': True,
        'collection_interval': 30,
        'prometheus': {
            'port': 8000,
            'path': '/metrics'
        },
        'custom_metrics': {
            'business_metrics_enabled': True,
            'performance_metrics_enabled': True
        }
    },
    'alerting': {
        'enabled': True,
        'alert_manager_url': env('ALERT_MANAGER_URL'),
        'notification_channels': {
            'email': env('ALERT_EMAIL'),
            'slack': env('SLACK_WEBHOOK_URL'),
            'pagerduty': env('PAGERDUTY_KEY')
        }
    },
    'dashboards': {
        'grafana_url': env('GRAFANA_URL'),
        'grafana_api_key': env('GRAFANA_API_KEY'),
        'auto_provision': True
    },
    'health_checks': {
        'enabled': True,
        'interval': 60,
        'endpoints': [
            '/api/health',
            '/api/scraper/health',
            '/api/generator/health',
            '/api/deploy/health'
        ]
    }
}
```

## Testing Strategy

### Monitoring Tests
```python
# tests/test_monitoring.py
import pytest
from monitoring.loggers.structured_logger import StructuredLogger
from monitoring.metrics.app_metrics import ApplicationMetrics

def test_structured_logging():
    logger = StructuredLogger('test_module')
    
    # Test log output format
    with pytest.LogCapture() as log_capture:
        logger.info("Test message", user_id="123", action="test")
        
        # Verify JSON structure
        log_entry = json.loads(log_capture.records[0].getMessage())
        assert log_entry['module'] == 'test_module'
        assert log_entry['message'] == 'Test message'
        assert log_entry['user_id'] == '123'

def test_metrics_collection():
    metrics = ApplicationMetrics()
    
    # Test metric recording
    metrics.record_trend_scraped('google_trends', 'US', 'Technology', 5)
    
    # Verify metric value
    metric_value = metrics.trends_scraped_total.labels(
        source='google_trends',
        region='US',
        category='Technology'
    )._value._value
    
    assert metric_value == 5
```

## Deployment Notes

### Environment Variables
```bash
# Logging Configuration
LOKI_URL=http://loki:3100
LOG_LEVEL=INFO
LOG_AGGREGATION_ENABLED=true

# Metrics Configuration
PROMETHEUS_PORT=8000
METRICS_COLLECTION_INTERVAL=30
GRAFANA_URL=http://grafana:3000
GRAFANA_API_KEY=your_grafana_api_key

# Alerting Configuration
ALERT_MANAGER_URL=http://alertmanager:9093
ALERT_EMAIL=<EMAIL>
SLACK_WEBHOOK_URL=your_slack_webhook
PAGERDUTY_KEY=your_pagerduty_key
```

### Docker Compose for Monitoring Stack
```yaml
# monitoring/docker-compose.yml
version: '3.8'
services:
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - ./alert_rules.yml:/etc/prometheus/alert_rules.yml
  
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana-storage:/var/lib/grafana
      - ./dashboards:/etc/grafana/provisioning/dashboards
  
  loki:
    image: grafana/loki:latest
    ports:
      - "3100:3100"
    volumes:
      - ./loki-config.yml:/etc/loki/local-config.yaml
  
  alertmanager:
    image: prom/alertmanager:latest
    ports:
      - "9093:9093"
    volumes:
      - ./alertmanager.yml:/etc/alertmanager/alertmanager.yml

volumes:
  grafana-storage:
```

## Success Criteria
- 100% system visibility through comprehensive logging
- Sub-second metrics collection and visualization
- Proactive alerting with <5 minute detection time
- Zero monitoring system downtime
- Complete performance baseline establishment
