# Trend Scraper Module - Implementation Plan

## Overview
The Trend Scraper Module is the entry point of the content pipeline, responsible for collecting trending topics from multiple sources including Google Trends and trends24.in. It processes, validates, and scores trends before storing them in the database for content generation.

## Dependencies
- **Database Module**: Stores scraped trends and metadata
- **Security Module**: API key management and secure credential storage
- **Monitoring Module**: Logging and performance metrics
- **Infrastructure Module**: Redis for caching and rate limiting

## Interfaces
### Outbound
- **Database**: Writes trend data to `trends` table
- **Celery**: Publishes scraping tasks to message queue
- **External APIs**: Google Trends, trends24.in, proxy services

### Inbound
- **Dashboard**: Manual trigger endpoints
- **Celery Beat**: Scheduled scraping tasks (every 15 minutes)
- **API**: Health checks and status monitoring

## Implementation Phases

### Phase 1: Core Scraping Infrastructure
**Duration**: Week 1-2

#### 1.1 Base Scraper Architecture
```python
# scraper/sources/base_scraper.py
from abc import ABC, abstractmethod
from typing import List, Dict, Optional
from dataclasses import dataclass

@dataclass
class TrendData:
    keyword: str
    search_volume: int
    growth_rate: float
    region: str
    category: str
    source: str
    timestamp: datetime

class BaseScraper(ABC):
    @abstractmethod
    async def scrape_trends(self, region: str, category: str) -> List[TrendData]:
        pass
    
    @abstractmethod
    def validate_data(self, data: TrendData) -> bool:
        pass
```

#### 1.2 Google Trends Scraper
```python
# scraper/sources/google_trends.py
from pytrends.request import TrendReq
import asyncio
from .base_scraper import BaseScraper, TrendData

class GoogleTrendsScraper(BaseScraper):
    def __init__(self, proxy_list: List[str]):
        self.proxy_rotation = ProxyRotator(proxy_list)
        self.rate_limiter = RateLimiter(requests_per_minute=10)
    
    async def scrape_trends(self, region: str, category: str) -> List[TrendData]:
        # Implementation with proxy rotation and rate limiting
        pass
```

#### 1.3 Trends24 Scraper
```python
# scraper/sources/trends24_scraper.py
import aiohttp
from bs4 import BeautifulSoup
from .base_scraper import BaseScraper, TrendData

class Trends24Scraper(BaseScraper):
    async def scrape_trends(self, region: str, category: str) -> List[TrendData]:
        # Web scraping implementation with error handling
        pass
```

### Phase 2: Advanced Features
**Duration**: Week 3-4

#### 2.1 Multi-Source Integration
```python
# scraper/core.py
class TrendOrchestrator:
    def __init__(self):
        self.scrapers = [
            GoogleTrendsScraper(proxy_list),
            Trends24Scraper(),
            # Additional scrapers
        ]
        self.deduplicator = FuzzyMatcher(threshold=0.85)
    
    async def scrape_all_sources(self) -> List[TrendData]:
        tasks = []
        for scraper in self.scrapers:
            for region in REGIONS:
                for category in CATEGORIES:
                    task = scraper.scrape_trends(region, category)
                    tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        return self.process_results(results)
```

#### 2.2 Duplicate Detection
```python
# scraper/deduplication.py
from fuzzywuzzy import fuzz
from typing import List

class FuzzyMatcher:
    def __init__(self, threshold: float = 0.85):
        self.threshold = threshold
    
    def deduplicate_trends(self, trends: List[TrendData]) -> List[TrendData]:
        unique_trends = []
        for trend in trends:
            if not self.is_duplicate(trend, unique_trends):
                unique_trends.append(trend)
        return unique_trends
```

## Key Components

### Directory Structure
```
scraper/
├── __init__.py
├── core.py                 # Main orchestration logic
├── config.py              # Configuration management
├── tasks.py               # Celery task definitions
├── sources/
│   ├── __init__.py
│   ├── base_scraper.py    # Abstract base class
│   ├── google_trends.py   # Google Trends implementation
│   ├── trends24_scraper.py # Trends24.in implementation
│   └── custom_scrapers.py # Additional source scrapers
├── filters.py             # Regional and category filters
├── validators.py          # Data validation rules
├── deduplication.py       # Fuzzy matching for duplicates
├── scoring.py             # Keyword scoring algorithm
└── utils/
    ├── proxy_rotation.py  # Proxy management
    ├── rate_limiting.py   # Rate limiting utilities
    └── error_handling.py  # Retry mechanisms
```

### Configuration
```python
# scraper/config.py
SCRAPER_CONFIG = {
    'regions': ['US', 'UK', 'CA', 'AU'],
    'categories': ['Technology', 'Health', 'Entertainment'],
    'proxy_rotation': {
        'enabled': True,
        'proxy_list': env.list('PROXY_LIST'),
        'rotation_interval': 300  # 5 minutes
    },
    'rate_limiting': {
        'google_trends': {'requests_per_minute': 10},
        'trends24': {'requests_per_minute': 30}
    },
    'scoring': {
        'search_volume_weight': 0.6,
        'growth_rate_weight': 0.4,
        'minimum_score': 50
    }
}
```

## Data Models

### Trend Data Structure
```python
# scraper/models.py
from pydantic import BaseModel
from datetime import datetime
from typing import Optional

class TrendInput(BaseModel):
    keyword: str
    search_volume: int
    growth_rate: float
    region: str
    category: str
    source: str
    raw_data: dict

class ProcessedTrend(BaseModel):
    keyword: str
    slug: str
    score: float
    region: str
    category: str
    search_volume: int
    growth_rate: float
    status: str = 'pending'
    created_at: datetime
    expire_at: Optional[datetime]
```

## API Endpoints

### Internal API Routes
```python
# scraper/api.py
from fastapi import APIRouter, Depends
from .tasks import scrape_trends_task

router = APIRouter(prefix="/api/scraper")

@router.post("/trigger")
async def trigger_scraping(
    regions: List[str] = None,
    categories: List[str] = None,
    current_user = Depends(get_current_user)
):
    """Manual trigger for scraping specific regions/categories"""
    task = scrape_trends_task.delay(regions, categories)
    return {"task_id": task.id, "status": "started"}

@router.get("/status/{task_id}")
async def get_scraping_status(task_id: str):
    """Get status of scraping task"""
    task = scrape_trends_task.AsyncResult(task_id)
    return {"status": task.status, "result": task.result}

@router.get("/health")
async def health_check():
    """Health check for scraper module"""
    return {"status": "healthy", "scrapers": len(scrapers)}
```

## Testing Strategy

### Unit Tests
```python
# tests/test_scrapers.py
import pytest
from scraper.sources.google_trends import GoogleTrendsScraper

@pytest.mark.asyncio
async def test_google_trends_scraper():
    scraper = GoogleTrendsScraper(proxy_list=[])
    trends = await scraper.scrape_trends('US', 'Technology')
    assert isinstance(trends, list)
    assert all(isinstance(t, TrendData) for t in trends)

@pytest.mark.asyncio
async def test_deduplication():
    deduplicator = FuzzyMatcher(threshold=0.85)
    trends = [
        TrendData(keyword="AI Technology", ...),
        TrendData(keyword="AI Tech", ...),  # Should be deduplicated
        TrendData(keyword="Machine Learning", ...)
    ]
    unique = deduplicator.deduplicate_trends(trends)
    assert len(unique) == 2
```

### Integration Tests
```python
# tests/test_integration.py
@pytest.mark.integration
async def test_full_scraping_pipeline():
    orchestrator = TrendOrchestrator()
    trends = await orchestrator.scrape_all_sources()
    
    # Verify trends are stored in database
    stored_trends = await db.fetch_recent_trends()
    assert len(stored_trends) > 0
```

## Deployment Notes

### Environment Variables
```bash
# Required environment variables
GOOGLE_TRENDS_API_KEY=your_api_key
PROXY_LIST=proxy1:port,proxy2:port
REDIS_URL=redis://localhost:6379
DATABASE_URL=postgresql://user:pass@host:port/db
CELERY_BROKER_URL=redis://localhost:6379/0
```

### Celery Task Configuration
```python
# scraper/tasks.py
from celery import Celery
from .core import TrendOrchestrator

@celery.task(bind=True, max_retries=3)
def scrape_trends_task(self, regions=None, categories=None):
    try:
        orchestrator = TrendOrchestrator()
        trends = orchestrator.scrape_all_sources()
        return {"trends_found": len(trends), "status": "success"}
    except Exception as exc:
        self.retry(countdown=60, exc=exc)
```

### Monitoring Integration
```python
# scraper/monitoring.py
import structlog
from prometheus_client import Counter, Histogram

logger = structlog.get_logger()

TRENDS_SCRAPED = Counter('trends_scraped_total', 'Total trends scraped', ['source', 'region'])
SCRAPING_DURATION = Histogram('scraping_duration_seconds', 'Time spent scraping')

def log_scraping_metrics(source: str, region: str, duration: float, count: int):
    TRENDS_SCRAPED.labels(source=source, region=region).inc(count)
    SCRAPING_DURATION.observe(duration)
    logger.info("Scraping completed", source=source, region=region, count=count)
```

## Success Criteria
- Successfully scrape 100+ trends daily across all sources
- Maintain 95% uptime for scraping services
- Process and deduplicate trends within 2 minutes
- Zero data loss during scraping operations
- Comprehensive error handling and retry mechanisms
