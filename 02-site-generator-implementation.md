# Site Generator Module - Implementation Plan

## Overview
The Site Generator Module transforms approved trends into complete static websites with AI-generated content, images, and optimized assets. It manages the entire content creation pipeline from template processing to Git repository management.

## Dependencies
- **Database Module**: Retrieves approved trends and stores generated content
- **Security Module**: API key management for AI services
- **Coolify Deploy Module**: Triggers deployment after content generation
- **Monitoring Module**: Logging and performance tracking
- **Infrastructure Module**: File storage and Git repository access

## Interfaces
### Outbound
- **AI Services**: OpenAI-compatible text and image generation APIs
- **Git Repository**: Commits generated content and assets
- **Coolify Deploy**: Triggers build and deployment
- **Database**: Stores content metadata and deployment status

### Inbound
- **Dashboard**: Manual content generation triggers
- **Celery**: Automated content generation tasks
- **API**: Content management and status endpoints

## Implementation Phases

### Phase 1: Template System
**Duration**: Week 2-3

#### 1.1 MDX Template Engine
```python
# generator/templates/engine.py
from jinja2 import Environment, FileSystemLoader
import frontmatter
from typing import Dict, Any

class MDXTemplateEngine:
    def __init__(self, template_dir: str):
        self.env = Environment(loader=FileSystemLoader(template_dir))
        self.env.filters.update({
            'slugify': self.slugify,
            'truncate_words': self.truncate_words,
            'format_date': self.format_date
        })
    
    def render_template(self, template_name: str, context: Dict[str, Any]) -> str:
        template = self.env.get_template(template_name)
        return template.render(**context)
    
    def generate_frontmatter(self, trend_data: Dict) -> Dict:
        return {
            'title': trend_data['title'],
            'description': trend_data['description'],
            'slug': trend_data['slug'],
            'publishedAt': trend_data['created_at'].isoformat(),
            'tags': trend_data.get('tags', []),
            'heroImage': trend_data.get('hero_image_url'),
            'category': trend_data['category'],
            'region': trend_data['region']
        }
```

#### 1.2 Template Structure
```markdown
<!-- templates/article.mdx -->
---
title: "{{ title }}"
description: "{{ description }}"
slug: "{{ slug }}"
publishedAt: "{{ published_at }}"
tags: {{ tags | tojson }}
heroImage: "{{ hero_image_url }}"
category: "{{ category }}"
region: "{{ region }}"
---

# {{ title }}

{{ hero_image_component }}

{{ content_body }}

{% if code_snippet %}
## Code Example

```{{ code_language }}
{{ code_snippet }}
```
{% endif %}

{{ related_trends_component }}
```

### Phase 2: AI Content Generation
**Duration**: Week 3-4

#### 2.1 AI Service Abstraction
```python
# generator/ai/base_ai_client.py
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional

class BaseAIClient(ABC):
    @abstractmethod
    async def generate_text(self, prompt: str, **kwargs) -> str:
        pass
    
    @abstractmethod
    async def generate_image(self, prompt: str, **kwargs) -> str:
        pass
    
    @abstractmethod
    def get_usage_stats(self) -> Dict[str, Any]:
        pass

# generator/ai/openai_client.py
import aiohttp
from .base_ai_client import BaseAIClient

class OpenAICompatibleClient(BaseAIClient):
    def __init__(self, api_key: str, base_url: str):
        self.api_key = api_key
        self.base_url = base_url
        self.session = aiohttp.ClientSession()
    
    async def generate_text(self, prompt: str, **kwargs) -> str:
        payload = {
            'model': kwargs.get('model', 'gpt-3.5-turbo'),
            'messages': [{'role': 'user', 'content': prompt}],
            'max_tokens': kwargs.get('max_tokens', 2000),
            'temperature': kwargs.get('temperature', 0.7)
        }
        
        async with self.session.post(
            f"{self.base_url}/chat/completions",
            headers={'Authorization': f'Bearer {self.api_key}'},
            json=payload
        ) as response:
            data = await response.json()
            return data['choices'][0]['message']['content']
```

#### 2.2 Content Generation Pipeline
```python
# generator/content_pipeline.py
from typing import Dict, Any
from .ai.openai_client import OpenAICompatibleClient
from .templates.engine import MDXTemplateEngine

class ContentGenerator:
    def __init__(self, ai_client: BaseAIClient, template_engine: MDXTemplateEngine):
        self.ai_client = ai_client
        self.template_engine = template_engine
        self.content_prompts = self.load_prompts()
    
    async def generate_article(self, trend_data: Dict) -> Dict[str, Any]:
        # Generate title and description
        title_prompt = self.content_prompts['title'].format(keyword=trend_data['keyword'])
        title = await self.ai_client.generate_text(title_prompt, max_tokens=100)
        
        # Generate main content
        content_prompt = self.content_prompts['article'].format(
            keyword=trend_data['keyword'],
            category=trend_data['category'],
            title=title
        )
        content = await self.ai_client.generate_text(content_prompt, max_tokens=1500)
        
        # Generate hero image
        image_prompt = self.content_prompts['image'].format(
            keyword=trend_data['keyword'],
            category=trend_data['category']
        )
        hero_image = await self.ai_client.generate_image(image_prompt)
        
        # Generate code snippet for tech topics
        code_snippet = None
        if trend_data['category'].lower() == 'technology':
            code_snippet = await self.generate_code_snippet(trend_data['keyword'])
        
        return {
            'title': title.strip(),
            'description': self.extract_description(content),
            'content_body': content,
            'hero_image_url': hero_image,
            'code_snippet': code_snippet,
            'slug': self.generate_slug(title),
            'tags': self.extract_tags(content, trend_data['keyword'])
        }
```

### Phase 3: Static File Management
**Duration**: Week 4-5

#### 3.1 Git Operations
```python
# generator/git_ops.py
import git
import os
from pathlib import Path
from typing import List, Dict

class GitManager:
    def __init__(self, repo_path: str, remote_url: str):
        self.repo_path = Path(repo_path)
        self.repo = git.Repo(repo_path) if os.path.exists(repo_path) else None
        self.remote_url = remote_url
    
    def clone_or_pull(self):
        if not self.repo:
            self.repo = git.Repo.clone_from(self.remote_url, self.repo_path)
        else:
            self.repo.remotes.origin.pull()
    
    def create_content_files(self, content_data: Dict, assets: List[str]) -> str:
        # Create content directory
        content_dir = self.repo_path / 'content' / content_data['slug']
        content_dir.mkdir(parents=True, exist_ok=True)
        
        # Write MDX file
        mdx_content = self.template_engine.render_template('article.mdx', content_data)
        mdx_file = content_dir / 'index.mdx'
        mdx_file.write_text(mdx_content, encoding='utf-8')
        
        # Copy assets
        assets_dir = content_dir / 'assets'
        assets_dir.mkdir(exist_ok=True)
        for asset in assets:
            # Copy and optimize assets
            self.copy_and_optimize_asset(asset, assets_dir)
        
        return str(content_dir)
    
    def commit_and_push(self, message: str, files: List[str]):
        # Add files to staging
        for file_path in files:
            self.repo.index.add([file_path])
        
        # Commit changes
        self.repo.index.commit(message)
        
        # Push to remote
        self.repo.remotes.origin.push()
```

#### 3.2 Asset Management
```python
# generator/assets.py
from PIL import Image
import aiohttp
import aiofiles
from pathlib import Path

class AssetManager:
    def __init__(self, assets_dir: str):
        self.assets_dir = Path(assets_dir)
        self.assets_dir.mkdir(parents=True, exist_ok=True)
    
    async def download_and_optimize_image(self, url: str, filename: str) -> str:
        # Download image
        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                image_data = await response.read()
        
        # Save original
        original_path = self.assets_dir / f"original_{filename}"
        async with aiofiles.open(original_path, 'wb') as f:
            await f.write(image_data)
        
        # Optimize and convert to WebP
        optimized_path = self.assets_dir / f"{filename.split('.')[0]}.webp"
        with Image.open(original_path) as img:
            # Resize if too large
            if img.width > 1200:
                ratio = 1200 / img.width
                new_height = int(img.height * ratio)
                img = img.resize((1200, new_height), Image.Resampling.LANCZOS)
            
            # Convert to WebP with optimization
            img.save(optimized_path, 'WebP', quality=85, optimize=True)
        
        # Remove original
        original_path.unlink()
        
        return str(optimized_path)
```

## Key Components

### Directory Structure
```
generator/
├── __init__.py
├── content_pipeline.py    # Main content generation logic
├── tasks.py              # Celery task definitions
├── config.py             # Configuration management
├── ai/
│   ├── __init__.py
│   ├── base_ai_client.py # Abstract AI client
│   ├── openai_client.py  # OpenAI-compatible implementation
│   └── anthropic_client.py # Alternative AI provider
├── templates/
│   ├── __init__.py
│   ├── engine.py         # Template engine
│   ├── article.mdx       # Article template
│   ├── landing.mdx       # Landing page template
│   └── components/       # Reusable template components
├── assets.py             # Asset management and optimization
├── git_ops.py           # Git repository operations
├── content_moderation.py # Content filtering and validation
└── utils/
    ├── slug_generator.py # URL slug generation
    ├── image_optimizer.py # Image processing utilities
    └── text_processor.py # Text processing helpers
```

### Configuration
```python
# generator/config.py
GENERATOR_CONFIG = {
    'ai_services': {
        'text_generation': {
            'provider': 'openai',
            'api_key': env('OPENAI_API_KEY'),
            'base_url': env('OPENAI_BASE_URL'),
            'model': 'gpt-3.5-turbo',
            'max_tokens': 2000,
            'temperature': 0.7
        },
        'image_generation': {
            'provider': 'openai',
            'api_key': env('OPENAI_API_KEY'),
            'model': 'dall-e-3',
            'size': '1024x1024',
            'quality': 'standard'
        }
    },
    'content': {
        'max_article_length': 2000,
        'min_article_length': 500,
        'include_code_snippets': True,
        'moderation_enabled': True
    },
    'git': {
        'repository_url': env('CONTENT_REPO_URL'),
        'branch': 'main',
        'commit_message_template': 'Add content for trend: {keyword}'
    },
    'assets': {
        'image_optimization': True,
        'webp_conversion': True,
        'max_image_width': 1200,
        'image_quality': 85
    }
}
```

## Data Models

### Content Data Structure
```python
# generator/models.py
from pydantic import BaseModel, HttpUrl
from datetime import datetime
from typing import List, Optional

class ContentRequest(BaseModel):
    trend_id: str
    keyword: str
    category: str
    region: str
    priority: int = 1

class GeneratedContent(BaseModel):
    trend_id: str
    title: str
    description: str
    content_body: str
    slug: str
    hero_image_url: Optional[HttpUrl]
    code_snippet: Optional[str]
    tags: List[str]
    meta_tags: dict
    created_at: datetime
    word_count: int
    readability_score: float

class ContentAsset(BaseModel):
    filename: str
    original_url: Optional[HttpUrl]
    optimized_path: str
    file_size: int
    mime_type: str
```

## API Endpoints

### Content Generation API
```python
# generator/api.py
from fastapi import APIRouter, Depends, HTTPException
from .tasks import generate_content_task
from .models import ContentRequest

router = APIRouter(prefix="/api/generator")

@router.post("/generate")
async def generate_content(
    request: ContentRequest,
    current_user = Depends(get_current_user)
):
    """Trigger content generation for a specific trend"""
    task = generate_content_task.delay(request.dict())
    return {"task_id": task.id, "status": "started"}

@router.get("/status/{task_id}")
async def get_generation_status(task_id: str):
    """Get status of content generation task"""
    task = generate_content_task.AsyncResult(task_id)
    return {"status": task.status, "result": task.result}

@router.post("/regenerate/{content_id}")
async def regenerate_content(
    content_id: str,
    sections: List[str] = None,  # ['title', 'content', 'image']
    current_user = Depends(get_current_user)
):
    """Regenerate specific sections of existing content"""
    task = regenerate_content_task.delay(content_id, sections)
    return {"task_id": task.id, "status": "started"}
```

## Testing Strategy

### Unit Tests
```python
# tests/test_content_generation.py
import pytest
from generator.content_pipeline import ContentGenerator
from generator.ai.openai_client import OpenAICompatibleClient

@pytest.mark.asyncio
async def test_content_generation():
    ai_client = MockAIClient()
    generator = ContentGenerator(ai_client, template_engine)
    
    trend_data = {
        'keyword': 'AI Technology',
        'category': 'Technology',
        'region': 'US'
    }
    
    content = await generator.generate_article(trend_data)
    
    assert content['title']
    assert content['content_body']
    assert len(content['content_body']) >= 500
    assert content['slug']

@pytest.mark.asyncio
async def test_asset_optimization():
    asset_manager = AssetManager('/tmp/test_assets')
    optimized_path = await asset_manager.download_and_optimize_image(
        'https://example.com/test.jpg',
        'test.jpg'
    )
    
    assert optimized_path.endswith('.webp')
    assert os.path.exists(optimized_path)
```

### Integration Tests
```python
# tests/test_integration.py
@pytest.mark.integration
async def test_full_generation_pipeline():
    # Test complete pipeline from trend to deployed content
    trend_id = await create_test_trend()
    
    # Generate content
    task = generate_content_task.delay({'trend_id': trend_id})
    result = task.get(timeout=300)
    
    # Verify content was created
    content = await db.fetch_content_by_trend_id(trend_id)
    assert content is not None
    
    # Verify files were committed to git
    assert git_manager.file_exists(f'content/{content.slug}/index.mdx')
```

## Deployment Notes

### Environment Variables
```bash
# AI Service Configuration
OPENAI_API_KEY=your_openai_api_key
OPENAI_BASE_URL=https://api.openai.com/v1
ANTHROPIC_API_KEY=your_anthropic_key

# Git Configuration
CONTENT_REPO_URL=https://github.com/user/content-repo.git
GIT_USERNAME=automation-bot
GIT_TOKEN=your_git_token

# Content Settings
MAX_ARTICLE_LENGTH=2000
CONTENT_MODERATION_ENABLED=true
IMAGE_OPTIMIZATION_ENABLED=true
```

### Celery Task Configuration
```python
# generator/tasks.py
from celery import Celery
from .content_pipeline import ContentGenerator

@celery.task(bind=True, max_retries=3)
def generate_content_task(self, trend_data):
    try:
        generator = ContentGenerator()
        content = generator.generate_article(trend_data)
        
        # Store in database
        content_id = db.store_content(content)
        
        # Trigger deployment
        deploy_task.delay(content_id)
        
        return {"content_id": content_id, "status": "success"}
    except Exception as exc:
        self.retry(countdown=60, exc=exc)
```

## Success Criteria
- Generate high-quality content within 5 minutes per trend
- Maintain 95% content approval rate (human review)
- Optimize images to reduce file size by 60%
- Successfully commit and push content to Git repository
- Zero content generation failures due to AI service issues
