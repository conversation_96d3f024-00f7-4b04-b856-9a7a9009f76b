# Database & Storage Module - Implementation Plan

## Overview
The Database & Storage Module provides the central data persistence layer using Supabase (PostgreSQL) with Row Level Security, comprehensive data models, and repository pattern implementation. It serves as the single source of truth for all system entities and relationships.

## Dependencies
- **Infrastructure Module**: Database server provisioning and configuration
- **Security Module**: Authentication tokens and encryption keys
- **Monitoring Module**: Database performance metrics and health monitoring

## Interfaces
### Outbound
- **Supabase API**: Database operations and real-time subscriptions
- **File Storage**: Asset and backup storage management
- **Monitoring**: Performance metrics and query analytics

### Inbound
- **All System Modules**: CRUD operations and data queries
- **Dashboard**: Real-time data access and analytics
- **API Layer**: RESTful endpoints and GraphQL queries

## Implementation Phases

### Phase 1: Schema Design & Migration System
**Duration**: Week 1-2

#### 1.1 Database Schema Definition
```sql
-- database/migrations/001_initial_schema.sql

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- User profiles table (extends Supabase auth.users)
CREATE TABLE user_profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  role TEXT CHECK (role IN ('admin', 'editor', 'viewer')) DEFAULT 'viewer',
  permissions TEXT[] DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Trends table
CREATE TABLE trends (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  keyword TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  status TEXT CHECK (status IN ('pending', 'approved', 'rejected', 'live', 'expired')) DEFAULT 'pending',
  region TEXT NOT NULL,
  category TEXT NOT NULL,
  search_volume INTEGER,
  growth_rate DECIMAL(5,2),
  score DECIMAL(5,2),
  source TEXT NOT NULL,
  raw_data JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expire_at TIMESTAMP WITH TIME ZONE,
  deployed_at TIMESTAMP WITH TIME ZONE,
  ads_enabled BOOLEAN DEFAULT true,
  created_by UUID REFERENCES auth.users(id),
  
  -- Indexes for performance
  CONSTRAINT trends_keyword_region_unique UNIQUE(keyword, region)
);

-- Content table
CREATE TABLE content (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  trend_id UUID REFERENCES trends(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  body TEXT NOT NULL,
  meta_tags JSONB DEFAULT '{}',
  hero_image_url TEXT,
  code_snippet TEXT,
  code_language TEXT,
  word_count INTEGER,
  readability_score DECIMAL(3,1),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id)
);

-- Deployments table
CREATE TABLE deployments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  trend_id UUID REFERENCES trends(id),
  content_id UUID REFERENCES content(id),
  status TEXT CHECK (status IN ('pending', 'building', 'success', 'failed', 'cancelled')) DEFAULT 'pending',
  coolify_deployment_uuid TEXT,
  build_log TEXT,
  deploy_url TEXT,
  error_message TEXT,
  progress INTEGER DEFAULT 0,
  build_duration INTEGER, -- seconds
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  deployed_at TIMESTAMP WITH TIME ZONE,
  triggered_by UUID REFERENCES auth.users(id)
);

-- DNS records table
CREATE TABLE dns_records (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  trend_id UUID REFERENCES trends(id),
  subdomain TEXT NOT NULL,
  target_url TEXT NOT NULL,
  cloudflare_record_id TEXT,
  status TEXT CHECK (status IN ('pending', 'active', 'updating', 'deleted', 'error')) DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  deleted_at TIMESTAMP WITH TIME ZONE
);

-- Analytics table
CREATE TABLE analytics (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  trend_id UUID REFERENCES trends(id),
  event_type TEXT NOT NULL, -- 'page_view', 'redirect', 'error', etc.
  event_data JSONB DEFAULT '{}',
  user_agent TEXT,
  ip_address INET,
  country TEXT,
  referrer TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- System logs table
CREATE TABLE system_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  level TEXT CHECK (level IN ('debug', 'info', 'warning', 'error', 'critical')) NOT NULL,
  module TEXT NOT NULL,
  message TEXT NOT NULL,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 1.2 Row Level Security Policies
```sql
-- database/migrations/002_rls_policies.sql

-- Enable RLS on all tables
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE trends ENABLE ROW LEVEL SECURITY;
ALTER TABLE content ENABLE ROW LEVEL SECURITY;
ALTER TABLE deployments ENABLE ROW LEVEL SECURITY;
ALTER TABLE dns_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE system_logs ENABLE ROW LEVEL SECURITY;

-- User profiles policies
CREATE POLICY "Users can view own profile" ON user_profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON user_profiles
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Admins can view all profiles" ON user_profiles
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Trends policies
CREATE POLICY "Anyone can view approved trends" ON trends
  FOR SELECT USING (status IN ('approved', 'live'));

CREATE POLICY "Editors can manage trends" ON trends
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE id = auth.uid() AND role IN ('admin', 'editor')
    )
  );

-- Content policies
CREATE POLICY "Anyone can view content for live trends" ON content
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM trends 
      WHERE trends.id = content.trend_id AND trends.status = 'live'
    )
  );

CREATE POLICY "Editors can manage content" ON content
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE id = auth.uid() AND role IN ('admin', 'editor')
    )
  );

-- Similar policies for other tables...
```

#### 1.3 Migration System
```python
# database/migrations/migration_manager.py
import asyncio
import asyncpg
from pathlib import Path
from typing import List, Dict
import logging

class MigrationManager:
    def __init__(self, database_url: str):
        self.database_url = database_url
        self.migrations_dir = Path(__file__).parent
        self.logger = logging.getLogger(__name__)
    
    async def create_migrations_table(self, conn: asyncpg.Connection):
        """Create migrations tracking table"""
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS schema_migrations (
                version TEXT PRIMARY KEY,
                applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            )
        """)
    
    async def get_applied_migrations(self, conn: asyncpg.Connection) -> List[str]:
        """Get list of applied migrations"""
        rows = await conn.fetch("SELECT version FROM schema_migrations ORDER BY version")
        return [row['version'] for row in rows]
    
    async def get_pending_migrations(self) -> List[Dict[str, str]]:
        """Get list of pending migrations"""
        migration_files = sorted(self.migrations_dir.glob("*.sql"))
        
        conn = await asyncpg.connect(self.database_url)
        try:
            await self.create_migrations_table(conn)
            applied = await self.get_applied_migrations(conn)
            
            pending = []
            for file_path in migration_files:
                version = file_path.stem
                if version not in applied:
                    pending.append({
                        'version': version,
                        'path': str(file_path),
                        'content': file_path.read_text()
                    })
            
            return pending
        finally:
            await conn.close()
    
    async def apply_migration(self, migration: Dict[str, str]):
        """Apply a single migration"""
        conn = await asyncpg.connect(self.database_url)
        try:
            async with conn.transaction():
                # Execute migration SQL
                await conn.execute(migration['content'])
                
                # Record migration as applied
                await conn.execute(
                    "INSERT INTO schema_migrations (version) VALUES ($1)",
                    migration['version']
                )
                
                self.logger.info(f"Applied migration: {migration['version']}")
        finally:
            await conn.close()
    
    async def migrate(self):
        """Apply all pending migrations"""
        pending = await self.get_pending_migrations()
        
        if not pending:
            self.logger.info("No pending migrations")
            return
        
        for migration in pending:
            await self.apply_migration(migration)
        
        self.logger.info(f"Applied {len(pending)} migrations")
```

### Phase 2: Data Models & Repository Pattern
**Duration**: Week 2-3

#### 2.1 Base Model & Repository
```python
# database/models/base_model.py
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, TypeVar, Generic
from datetime import datetime
import asyncpg
from pydantic import BaseModel

T = TypeVar('T', bound=BaseModel)

class BaseRepository(Generic[T], ABC):
    def __init__(self, connection_pool: asyncpg.Pool, table_name: str):
        self.pool = connection_pool
        self.table_name = table_name
    
    async def create(self, data: Dict[str, Any]) -> str:
        """Create new record and return ID"""
        columns = list(data.keys())
        placeholders = [f"${i+1}" for i in range(len(columns))]
        values = list(data.values())
        
        query = f"""
            INSERT INTO {self.table_name} ({', '.join(columns)})
            VALUES ({', '.join(placeholders)})
            RETURNING id
        """
        
        async with self.pool.acquire() as conn:
            row = await conn.fetchrow(query, *values)
            return str(row['id'])
    
    async def get_by_id(self, record_id: str) -> Optional[Dict[str, Any]]:
        """Get record by ID"""
        query = f"SELECT * FROM {self.table_name} WHERE id = $1"
        
        async with self.pool.acquire() as conn:
            row = await conn.fetchrow(query, record_id)
            return dict(row) if row else None
    
    async def update(self, record_id: str, data: Dict[str, Any]) -> bool:
        """Update record by ID"""
        if not data:
            return False
        
        # Add updated_at timestamp
        data['updated_at'] = datetime.utcnow()
        
        set_clauses = [f"{key} = ${i+2}" for i, key in enumerate(data.keys())]
        values = [record_id] + list(data.values())
        
        query = f"""
            UPDATE {self.table_name} 
            SET {', '.join(set_clauses)}
            WHERE id = $1
        """
        
        async with self.pool.acquire() as conn:
            result = await conn.execute(query, *values)
            return result.split()[-1] == '1'
    
    async def delete(self, record_id: str) -> bool:
        """Delete record by ID"""
        query = f"DELETE FROM {self.table_name} WHERE id = $1"
        
        async with self.pool.acquire() as conn:
            result = await conn.execute(query, record_id)
            return result.split()[-1] == '1'
    
    async def list_with_pagination(
        self, 
        page: int = 1, 
        page_size: int = 20,
        filters: Optional[Dict[str, Any]] = None,
        order_by: str = 'created_at DESC'
    ) -> Dict[str, Any]:
        """List records with pagination and filtering"""
        offset = (page - 1) * page_size
        where_clauses = []
        values = []
        
        if filters:
            for key, value in filters.items():
                where_clauses.append(f"{key} = ${len(values) + 1}")
                values.append(value)
        
        where_sql = f"WHERE {' AND '.join(where_clauses)}" if where_clauses else ""
        
        # Count total records
        count_query = f"SELECT COUNT(*) FROM {self.table_name} {where_sql}"
        
        # Get paginated records
        data_query = f"""
            SELECT * FROM {self.table_name} 
            {where_sql}
            ORDER BY {order_by}
            LIMIT ${len(values) + 1} OFFSET ${len(values) + 2}
        """
        
        async with self.pool.acquire() as conn:
            total_count = await conn.fetchval(count_query, *values)
            rows = await conn.fetch(data_query, *values, page_size, offset)
            
            return {
                'data': [dict(row) for row in rows],
                'pagination': {
                    'page': page,
                    'page_size': page_size,
                    'total_count': total_count,
                    'total_pages': (total_count + page_size - 1) // page_size
                }
            }
```

#### 2.2 Trend Model Implementation
```python
# database/models/trend_model.py
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from .base_model import BaseRepository
from pydantic import BaseModel

class TrendData(BaseModel):
    id: Optional[str] = None
    keyword: str
    slug: str
    status: str = 'pending'
    region: str
    category: str
    search_volume: Optional[int] = None
    growth_rate: Optional[float] = None
    score: Optional[float] = None
    source: str
    raw_data: Optional[Dict] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    expire_at: Optional[datetime] = None
    deployed_at: Optional[datetime] = None
    ads_enabled: bool = True

class TrendRepository(BaseRepository[TrendData]):
    def __init__(self, connection_pool):
        super().__init__(connection_pool, 'trends')
    
    async def get_by_slug(self, slug: str) -> Optional[Dict[str, Any]]:
        """Get trend by slug"""
        query = "SELECT * FROM trends WHERE slug = $1"
        
        async with self.pool.acquire() as conn:
            row = await conn.fetchrow(query, slug)
            return dict(row) if row else None
    
    async def get_pending_trends(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get pending trends for approval"""
        query = """
            SELECT * FROM trends 
            WHERE status = 'pending'
            ORDER BY score DESC, created_at ASC
            LIMIT $1
        """
        
        async with self.pool.acquire() as conn:
            rows = await conn.fetch(query, limit)
            return [dict(row) for row in rows]
    
    async def get_live_trends(self, region: str = None) -> List[Dict[str, Any]]:
        """Get currently live trends"""
        where_clause = "WHERE status = 'live'"
        values = []
        
        if region:
            where_clause += " AND region = $1"
            values.append(region)
        
        query = f"""
            SELECT * FROM trends 
            {where_clause}
            AND (expire_at IS NULL OR expire_at > NOW())
            ORDER BY deployed_at DESC
        """
        
        async with self.pool.acquire() as conn:
            rows = await conn.fetch(query, *values)
            return [dict(row) for row in rows]
    
    async def get_expired_trends(self) -> List[Dict[str, Any]]:
        """Get trends that have expired"""
        query = """
            SELECT * FROM trends 
            WHERE status = 'live' 
            AND expire_at IS NOT NULL 
            AND expire_at <= NOW()
        """
        
        async with self.pool.acquire() as conn:
            rows = await conn.fetch(query)
            return [dict(row) for row in rows]
    
    async def approve_trend(self, trend_id: str, approved_by: str) -> bool:
        """Approve a pending trend"""
        return await self.update(trend_id, {
            'status': 'approved',
            'updated_at': datetime.utcnow()
        })
    
    async def reject_trend(self, trend_id: str, rejected_by: str) -> bool:
        """Reject a pending trend"""
        return await self.update(trend_id, {
            'status': 'rejected',
            'updated_at': datetime.utcnow()
        })
    
    async def mark_as_live(self, trend_id: str, deploy_url: str) -> bool:
        """Mark trend as live after successful deployment"""
        expire_at = datetime.utcnow() + timedelta(days=14)  # 14-day TTL
        
        return await self.update(trend_id, {
            'status': 'live',
            'deployed_at': datetime.utcnow(),
            'expire_at': expire_at
        })
    
    async def get_trending_keywords(self, days: int = 7) -> List[Dict[str, Any]]:
        """Get trending keywords analytics"""
        query = """
            SELECT 
                keyword,
                category,
                region,
                AVG(search_volume) as avg_search_volume,
                AVG(growth_rate) as avg_growth_rate,
                COUNT(*) as frequency
            FROM trends 
            WHERE created_at >= NOW() - INTERVAL '%s days'
            AND status IN ('approved', 'live')
            GROUP BY keyword, category, region
            ORDER BY avg_search_volume DESC, frequency DESC
            LIMIT 100
        """ % days
        
        async with self.pool.acquire() as conn:
            rows = await conn.fetch(query)
            return [dict(row) for row in rows]
```

## Key Components

### Directory Structure
```
database/
├── __init__.py
├── connection.py         # Database connection management
├── config.py            # Database configuration
├── migrations/
│   ├── migration_manager.py
│   ├── 001_initial_schema.sql
│   ├── 002_rls_policies.sql
│   └── 003_indexes_and_triggers.sql
├── models/
│   ├── __init__.py
│   ├── base_model.py    # Base repository pattern
│   ├── trend_model.py   # Trend entity operations
│   ├── content_model.py # Content entity operations
│   ├── deployment_model.py # Deployment tracking
│   ├── dns_model.py     # DNS record management
│   └── analytics_model.py # Analytics data
├── repositories/
│   ├── __init__.py
│   └── repository_factory.py
├── validators/
│   ├── __init__.py
│   ├── trend_validator.py
│   └── content_validator.py
└── utils/
    ├── backup_manager.py
    ├── performance_monitor.py
    └── data_seeder.py
```

### Configuration
```python
# database/config.py
import os
from typing import Dict, Any

DATABASE_CONFIG = {
    'supabase': {
        'url': os.getenv('SUPABASE_URL'),
        'service_role_key': os.getenv('SUPABASE_SERVICE_ROLE_KEY'),
        'anon_key': os.getenv('SUPABASE_ANON_KEY'),
    },
    'connection': {
        'min_connections': 5,
        'max_connections': 20,
        'connection_timeout': 30,
        'command_timeout': 60,
    },
    'performance': {
        'enable_query_logging': True,
        'slow_query_threshold': 1000,  # milliseconds
        'enable_connection_pooling': True,
    },
    'backup': {
        'enabled': True,
        'schedule': '0 2 * * *',  # Daily at 2 AM
        'retention_days': 30,
        'storage_location': 's3://backups/database/'
    }
}
```

## Testing Strategy

### Unit Tests
```python
# tests/test_trend_model.py
import pytest
from database.models.trend_model import TrendRepository
from database.connection import get_test_connection_pool

@pytest.fixture
async def trend_repo():
    pool = await get_test_connection_pool()
    return TrendRepository(pool)

@pytest.mark.asyncio
async def test_create_trend(trend_repo):
    trend_data = {
        'keyword': 'Test Trend',
        'slug': 'test-trend',
        'region': 'US',
        'category': 'Technology',
        'source': 'test',
        'search_volume': 1000,
        'growth_rate': 15.5
    }
    
    trend_id = await trend_repo.create(trend_data)
    assert trend_id
    
    # Verify trend was created
    trend = await trend_repo.get_by_id(trend_id)
    assert trend['keyword'] == 'Test Trend'
    assert trend['status'] == 'pending'

@pytest.mark.asyncio
async def test_approve_trend(trend_repo):
    # Create test trend
    trend_id = await create_test_trend(trend_repo)
    
    # Approve trend
    success = await trend_repo.approve_trend(trend_id, 'test-user')
    assert success
    
    # Verify status changed
    trend = await trend_repo.get_by_id(trend_id)
    assert trend['status'] == 'approved'
```

## Deployment Notes

### Environment Variables
```bash
# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
SUPABASE_ANON_KEY=your_anon_key

# Database Configuration
DATABASE_URL=postgresql://user:pass@host:port/dbname
DB_POOL_MIN_CONNECTIONS=5
DB_POOL_MAX_CONNECTIONS=20

# Performance Settings
ENABLE_QUERY_LOGGING=true
SLOW_QUERY_THRESHOLD=1000
```

### Migration Commands
```bash
# Apply all pending migrations
python -m database.migrations.migration_manager migrate

# Create new migration
python -m database.migrations.migration_manager create "add_user_preferences_table"

# Rollback last migration
python -m database.migrations.migration_manager rollback
```

## Success Criteria
- Zero data loss during operations
- Sub-100ms query response times for 95% of queries
- 99.9% database uptime
- Successful automated backups and recovery
- Comprehensive audit trail for all data changes
