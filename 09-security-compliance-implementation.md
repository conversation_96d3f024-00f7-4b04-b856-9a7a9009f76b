# Security & Compliance Module - Implementation Plan

## Overview
The Security & Compliance Module provides comprehensive security controls including secret management, authentication, authorization, input validation, and security monitoring. It implements defense-in-depth strategies using HashiCorp Vault, security middleware, and compliance frameworks.

## Dependencies
- **Infrastructure Module**: Secure infrastructure provisioning and network security
- **Database Module**: Secure data access and encryption at rest
- **Monitoring Module**: Security event logging and threat detection
- **All System Modules**: Security policy enforcement and audit trails

## Interfaces
### Outbound
- **HashiCorp Vault**: Secret storage and rotation management
- **External Auth Providers**: OAuth, SAML, and SSO integrations
- **Security Scanners**: Vulnerability assessment and compliance checking
- **Audit Systems**: Security event logging and compliance reporting

### Inbound
- **All System Modules**: Authentication and authorization requests
- **API Gateway**: Security policy enforcement
- **Dashboard**: Security configuration and monitoring
- **External Auditors**: Compliance reporting and access logs

## Implementation Phases

### Phase 1: Secret Management
**Duration**: Week 8-9

#### 1.1 HashiCorp Vault Setup
```python
# security/vault/vault_client.py
import hvac
import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import json

class VaultClient:
    def __init__(self, vault_url: str, vault_token: str = None):
        self.vault_url = vault_url
        self.client = hvac.Client(url=vault_url, token=vault_token)
        self.mount_point = 'secret'
        self.auth_method = 'token'

    async def authenticate(self, auth_method: str = 'token', **auth_params):
        """Authenticate with Vault using specified method"""
        try:
            if auth_method == 'token':
                # Token authentication (already set in constructor)
                if not self.client.is_authenticated():
                    raise Exception("Token authentication failed")

            elif auth_method == 'kubernetes':
                # Kubernetes service account authentication
                jwt_token = auth_params.get('jwt_token')
                role = auth_params.get('role')

                auth_response = self.client.auth.kubernetes.login(
                    role=role,
                    jwt=jwt_token
                )
                self.client.token = auth_response['auth']['client_token']

            elif auth_method == 'aws':
                # AWS IAM authentication
                self.client.auth.aws.iam_login(
                    access_key=auth_params.get('access_key'),
                    secret_key=auth_params.get('secret_key'),
                    session_token=auth_params.get('session_token'),
                    role=auth_params.get('role')
                )

            return True

        except Exception as e:
            raise Exception(f"Vault authentication failed: {str(e)}")

    async def store_secret(self, path: str, secret_data: Dict[str, Any]) -> bool:
        """Store secret in Vault"""
        try:
            # Add metadata
            secret_with_metadata = {
                **secret_data,
                'created_at': datetime.utcnow().isoformat(),
                'created_by': 'trend-platform'
            }

            response = self.client.secrets.kv.v2.create_or_update_secret(
                path=path,
                secret=secret_with_metadata,
                mount_point=self.mount_point
            )

            return response is not None

        except Exception as e:
            raise Exception(f"Failed to store secret at {path}: {str(e)}")

    async def get_secret(self, path: str) -> Optional[Dict[str, Any]]:
        """Retrieve secret from Vault"""
        try:
            response = self.client.secrets.kv.v2.read_secret_version(
                path=path,
                mount_point=self.mount_point
            )

            if response and 'data' in response and 'data' in response['data']:
                return response['data']['data']

            return None

        except Exception as e:
            if 'not found' in str(e).lower():
                return None
            raise Exception(f"Failed to retrieve secret from {path}: {str(e)}")

    async def delete_secret(self, path: str) -> bool:
        """Delete secret from Vault"""
        try:
            self.client.secrets.kv.v2.delete_metadata_and_all_versions(
                path=path,
                mount_point=self.mount_point
            )
            return True

        except Exception as e:
            raise Exception(f"Failed to delete secret at {path}: {str(e)}")

    async def rotate_secret(self, path: str, new_secret_data: Dict[str, Any]) -> bool:
        """Rotate secret with versioning"""
        try:
            # Get current secret for backup
            current_secret = await self.get_secret(path)

            # Store new version
            success = await self.store_secret(path, new_secret_data)

            if success and current_secret:
                # Store previous version as backup
                backup_path = f"{path}_backup_{int(datetime.utcnow().timestamp())}"
                await self.store_secret(backup_path, current_secret)

            return success

        except Exception as e:
            raise Exception(f"Failed to rotate secret at {path}: {str(e)}")

    async def list_secrets(self, path: str = '') -> List[str]:
        """List secrets at given path"""
        try:
            response = self.client.secrets.kv.v2.list_secrets(
                path=path,
                mount_point=self.mount_point
            )

            if response and 'data' in response and 'keys' in response['data']:
                return response['data']['keys']

            return []

        except Exception as e:
            raise Exception(f"Failed to list secrets at {path}: {str(e)}")

class SecretManager:
    def __init__(self, vault_client: VaultClient):
        self.vault = vault_client
        self.secret_cache = {}
        self.cache_ttl = 300  # 5 minutes

    async def get_api_key(self, service: str) -> Optional[str]:
        """Get API key for external service"""
        secret_path = f"api_keys/{service}"
        secret_data = await self.vault.get_secret(secret_path)

        if secret_data and 'api_key' in secret_data:
            return secret_data['api_key']

        return None

    async def get_database_credentials(self, environment: str) -> Optional[Dict[str, str]]:
        """Get database credentials"""
        secret_path = f"database/{environment}"
        secret_data = await self.vault.get_secret(secret_path)

        if secret_data:
            return {
                'host': secret_data.get('host'),
                'port': secret_data.get('port'),
                'username': secret_data.get('username'),
                'password': secret_data.get('password'),
                'database': secret_data.get('database')
            }

        return None

    async def rotate_api_keys(self) -> Dict[str, bool]:
        """Rotate all API keys"""
        results = {}

        # Get list of API key secrets
        api_keys = await self.vault.list_secrets('api_keys')

        for key_path in api_keys:
            try:
                # Generate new API key (implementation depends on service)
                new_key = await self.generate_new_api_key(key_path)

                if new_key:
                    success = await self.vault.rotate_secret(
                        f"api_keys/{key_path}",
                        {'api_key': new_key}
                    )
                    results[key_path] = success
                else:
                    results[key_path] = False

            except Exception as e:
                results[key_path] = False

        return results

    async def generate_new_api_key(self, service: str) -> Optional[str]:
        """Generate new API key for service (placeholder)"""
        # This would integrate with each service's API key generation
        # For now, return a placeholder
        import secrets
        return secrets.token_urlsafe(32)
```

#### 1.2 API Key Rotation System
```python
# security/rotation/key_rotator.py
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any
from security.vault.vault_client import SecretManager
from monitoring.loggers.structured_logger import StructuredLogger

class APIKeyRotator:
    def __init__(self, secret_manager: SecretManager):
        self.secret_manager = secret_manager
        self.logger = StructuredLogger('api_key_rotator')
        self.rotation_schedule = {
            'openai': 30,  # days
            'cloudflare': 90,
            'github': 60,
            'supabase': 90
        }

    async def rotate_keys_by_schedule(self) -> Dict[str, Any]:
        """Rotate API keys based on schedule"""
        rotation_results = {
            'rotated': [],
            'failed': [],
            'skipped': []
        }

        for service, rotation_days in self.rotation_schedule.items():
            try:
                needs_rotation = await self.check_rotation_needed(service, rotation_days)

                if needs_rotation:
                    success = await self.rotate_service_key(service)

                    if success:
                        rotation_results['rotated'].append(service)
                        self.logger.info(
                            "API key rotated successfully",
                            service=service,
                            operation="key_rotation"
                        )
                    else:
                        rotation_results['failed'].append(service)
                        self.logger.error(
                            "API key rotation failed",
                            service=service,
                            operation="key_rotation"
                        )
                else:
                    rotation_results['skipped'].append(service)

            except Exception as e:
                rotation_results['failed'].append(service)
                self.logger.error(
                    "API key rotation error",
                    service=service,
                    error=str(e),
                    operation="key_rotation"
                )

        return rotation_results

    async def check_rotation_needed(self, service: str, rotation_days: int) -> bool:
        """Check if API key needs rotation"""
        try:
            secret_data = await self.secret_manager.vault.get_secret(f"api_keys/{service}")

            if not secret_data or 'created_at' not in secret_data:
                return True  # No creation date, rotate to be safe

            created_at = datetime.fromisoformat(secret_data['created_at'])
            rotation_due = created_at + timedelta(days=rotation_days)

            return datetime.utcnow() >= rotation_due

        except Exception as e:
            self.logger.warning(
                "Could not check rotation schedule",
                service=service,
                error=str(e)
            )
            return False

    async def rotate_service_key(self, service: str) -> bool:
        """Rotate API key for specific service"""
        try:
            # Get current key for backup
            current_secret = await self.secret_manager.vault.get_secret(f"api_keys/{service}")

            # Generate new key based on service
            new_key = await self.generate_service_key(service)

            if not new_key:
                return False

            # Test new key before storing
            if await self.test_api_key(service, new_key):
                # Store new key
                success = await self.secret_manager.vault.rotate_secret(
                    f"api_keys/{service}",
                    {
                        'api_key': new_key,
                        'rotated_at': datetime.utcnow().isoformat(),
                        'previous_key_backup': current_secret.get('api_key') if current_secret else None
                    }
                )

                if success:
                    # Notify services of key rotation
                    await self.notify_key_rotation(service, new_key)

                return success
            else:
                self.logger.error(
                    "New API key failed validation",
                    service=service,
                    operation="key_rotation"
                )
                return False

        except Exception as e:
            self.logger.error(
                "API key rotation failed",
                service=service,
                error=str(e),
                operation="key_rotation"
            )
            return False

    async def generate_service_key(self, service: str) -> Optional[str]:
        """Generate new API key for specific service"""
        # This would integrate with each service's API
        # For demonstration, using placeholder logic

        if service == 'openai':
            # Would call OpenAI API to generate new key
            return await self.generate_openai_key()
        elif service == 'cloudflare':
            # Would call Cloudflare API to generate new key
            return await self.generate_cloudflare_key()
        elif service == 'github':
            # Would call GitHub API to generate new key
            return await self.generate_github_key()
        else:
            # Generic key generation
            import secrets
            return secrets.token_urlsafe(32)

    async def test_api_key(self, service: str, api_key: str) -> bool:
        """Test API key validity"""
        # Implementation would test each service's API
        # For now, return True as placeholder
        return True

    async def notify_key_rotation(self, service: str, new_key: str):
        """Notify services of key rotation"""
        # This would update configuration in running services
        # Could use message queue, webhook, or configuration reload
        pass
```

### Phase 2: Security Hardening
**Duration**: Week 10

#### 2.1 Security Middleware
```python
# security/middleware/security_middleware.py
from fastapi import Request, HTTPException, Depends
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response
import time
import hashlib
from typing import Dict, Any, Optional
from collections import defaultdict, deque
import asyncio

class SecurityMiddleware(BaseHTTPMiddleware):
    def __init__(self, app, config: Dict[str, Any]):
        super().__init__(app)
        self.config = config
        self.rate_limiter = RateLimiter(config.get('rate_limiting', {}))
        self.request_validator = RequestValidator(config.get('validation', {}))
        self.security_headers = SecurityHeaders(config.get('headers', {}))

    async def dispatch(self, request: Request, call_next):
        # Rate limiting
        if not await self.rate_limiter.check_rate_limit(request):
            raise HTTPException(status_code=429, detail="Rate limit exceeded")

        # Request validation
        await self.request_validator.validate_request(request)

        # Process request
        response = await call_next(request)

        # Add security headers
        response = self.security_headers.add_headers(response)

        return response

class RateLimiter:
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.requests = defaultdict(deque)
        self.blocked_ips = set()
        self.cleanup_interval = 60  # seconds

        # Start cleanup task
        asyncio.create_task(self.cleanup_old_requests())

    async def check_rate_limit(self, request: Request) -> bool:
        """Check if request is within rate limits"""
        client_ip = self.get_client_ip(request)

        # Check if IP is blocked
        if client_ip in self.blocked_ips:
            return False

        current_time = time.time()
        endpoint = f"{request.method}:{request.url.path}"

        # Get rate limit for endpoint
        limit_config = self.get_limit_config(endpoint)
        if not limit_config:
            return True

        requests_per_window = limit_config['requests']
        window_seconds = limit_config['window']

        # Clean old requests
        client_requests = self.requests[client_ip]
        while client_requests and client_requests[0] < current_time - window_seconds:
            client_requests.popleft()

        # Check if limit exceeded
        if len(client_requests) >= requests_per_window:
            # Block IP if severely exceeding limits
            if len(client_requests) > requests_per_window * 2:
                self.blocked_ips.add(client_ip)
                asyncio.create_task(self.unblock_ip_after_delay(client_ip, 3600))  # 1 hour

            return False

        # Add current request
        client_requests.append(current_time)
        return True

    def get_client_ip(self, request: Request) -> str:
        """Get client IP address"""
        # Check for forwarded headers
        forwarded_for = request.headers.get('X-Forwarded-For')
        if forwarded_for:
            return forwarded_for.split(',')[0].strip()

        real_ip = request.headers.get('X-Real-IP')
        if real_ip:
            return real_ip

        return request.client.host

    def get_limit_config(self, endpoint: str) -> Optional[Dict[str, int]]:
        """Get rate limit configuration for endpoint"""
        # Default limits
        default_limits = {
            'requests': 100,
            'window': 3600  # 1 hour
        }

        # Endpoint-specific limits
        endpoint_limits = {
            'POST:/api/scraper/trigger': {'requests': 10, 'window': 3600},
            'POST:/api/generator/generate': {'requests': 20, 'window': 3600},
            'POST:/api/deploy/trigger': {'requests': 5, 'window': 3600},
        }

        return endpoint_limits.get(endpoint, default_limits)

    async def cleanup_old_requests(self):
        """Periodically clean up old request records"""
        while True:
            await asyncio.sleep(self.cleanup_interval)
            current_time = time.time()

            for client_ip in list(self.requests.keys()):
                client_requests = self.requests[client_ip]

                # Remove requests older than 1 hour
                while client_requests and client_requests[0] < current_time - 3600:
                    client_requests.popleft()

                # Remove empty deques
                if not client_requests:
                    del self.requests[client_ip]

    async def unblock_ip_after_delay(self, ip: str, delay: int):
        """Unblock IP after specified delay"""
        await asyncio.sleep(delay)
        self.blocked_ips.discard(ip)

class RequestValidator:
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.max_request_size = config.get('max_request_size', 10 * 1024 * 1024)  # 10MB
        self.allowed_content_types = config.get('allowed_content_types', [
            'application/json',
            'application/x-www-form-urlencoded',
            'multipart/form-data'
        ])

    async def validate_request(self, request: Request):
        """Validate incoming request"""
        # Check request size
        content_length = request.headers.get('content-length')
        if content_length and int(content_length) > self.max_request_size:
            raise HTTPException(status_code=413, detail="Request too large")

        # Check content type for POST/PUT requests
        if request.method in ['POST', 'PUT', 'PATCH']:
            content_type = request.headers.get('content-type', '').split(';')[0]
            if content_type and content_type not in self.allowed_content_types:
                raise HTTPException(status_code=415, detail="Unsupported media type")

        # Validate headers
        await self.validate_headers(request)

        # Check for suspicious patterns
        await self.check_suspicious_patterns(request)

    async def validate_headers(self, request: Request):
        """Validate request headers"""
        # Check for required headers
        if request.method in ['POST', 'PUT', 'PATCH']:
            if not request.headers.get('content-type'):
                raise HTTPException(status_code=400, detail="Content-Type header required")

        # Check for suspicious headers
        suspicious_headers = ['x-forwarded-host', 'x-original-url', 'x-rewrite-url']
        for header in suspicious_headers:
            if header in request.headers:
                # Log suspicious activity
                pass

    async def check_suspicious_patterns(self, request: Request):
        """Check for suspicious request patterns"""
        url_path = str(request.url.path).lower()

        # Check for common attack patterns
        suspicious_patterns = [
            '../', '..\\', '/etc/passwd', '/proc/', 'cmd.exe',
            '<script', 'javascript:', 'vbscript:', 'onload=',
            'union select', 'drop table', 'insert into'
        ]

        for pattern in suspicious_patterns:
            if pattern in url_path:
                raise HTTPException(status_code=400, detail="Suspicious request pattern detected")

class SecurityHeaders:
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.default_headers = {
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY',
            'X-XSS-Protection': '1; mode=block',
            'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
            'Referrer-Policy': 'strict-origin-when-cross-origin',
            'Content-Security-Policy': self.build_csp_header()
        }

    def add_headers(self, response: Response) -> Response:
        """Add security headers to response"""
        for header, value in self.default_headers.items():
            response.headers[header] = value

        return response

    def build_csp_header(self) -> str:
        """Build Content Security Policy header"""
        csp_directives = [
            "default-src 'self'",
            "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
            "style-src 'self' 'unsafe-inline'",
            "img-src 'self' data: https:",
            "font-src 'self'",
            "connect-src 'self' https:",
            "frame-ancestors 'none'",
            "base-uri 'self'",
            "form-action 'self'"
        ]

        return '; '.join(csp_directives)

#### 2.2 Input Validation & Sanitization
```python
# security/validation/input_validator.py
import re
import html
from typing import Any, Dict, List, Optional
from pydantic import BaseModel, validator
import bleach

class InputValidator:
    def __init__(self):
        self.allowed_html_tags = ['p', 'br', 'strong', 'em', 'ul', 'ol', 'li']
        self.allowed_attributes = {}

        # Common regex patterns
        self.patterns = {
            'email': re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'),
            'slug': re.compile(r'^[a-z0-9-]+$'),
            'url': re.compile(r'^https?://[^\s/$.?#].[^\s]*$'),
            'uuid': re.compile(r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'),
            'alphanumeric': re.compile(r'^[a-zA-Z0-9]+$'),
            'safe_text': re.compile(r'^[a-zA-Z0-9\s\-_.,!?]+$')
        }

    def sanitize_html(self, content: str) -> str:
        """Sanitize HTML content"""
        if not content:
            return ''

        # Remove potentially dangerous content
        cleaned = bleach.clean(
            content,
            tags=self.allowed_html_tags,
            attributes=self.allowed_attributes,
            strip=True
        )

        return cleaned

    def sanitize_text(self, text: str) -> str:
        """Sanitize plain text input"""
        if not text:
            return ''

        # HTML escape
        sanitized = html.escape(text)

        # Remove null bytes and control characters
        sanitized = ''.join(char for char in sanitized if ord(char) >= 32 or char in '\t\n\r')

        return sanitized.strip()

    def validate_pattern(self, value: str, pattern_name: str) -> bool:
        """Validate value against named pattern"""
        if pattern_name not in self.patterns:
            return False

        return bool(self.patterns[pattern_name].match(value))

    def validate_length(self, value: str, min_length: int = 0, max_length: int = None) -> bool:
        """Validate string length"""
        if len(value) < min_length:
            return False

        if max_length and len(value) > max_length:
            return False

        return True

    def validate_sql_injection(self, value: str) -> bool:
        """Check for SQL injection patterns"""
        sql_patterns = [
            r"(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)",
            r"(\b(OR|AND)\s+\d+\s*=\s*\d+)",
            r"(\b(OR|AND)\s+['\"]?\w+['\"]?\s*=\s*['\"]?\w+['\"]?)",
            r"(--|#|/\*|\*/)",
            r"(\bxp_cmdshell\b|\bsp_executesql\b)"
        ]

        for pattern in sql_patterns:
            if re.search(pattern, value, re.IGNORECASE):
                return False

        return True

    def validate_xss(self, value: str) -> bool:
        """Check for XSS patterns"""
        xss_patterns = [
            r"<script[^>]*>.*?</script>",
            r"javascript:",
            r"vbscript:",
            r"onload\s*=",
            r"onerror\s*=",
            r"onclick\s*=",
            r"<iframe[^>]*>",
            r"<object[^>]*>",
            r"<embed[^>]*>"
        ]

        for pattern in xss_patterns:
            if re.search(pattern, value, re.IGNORECASE):
                return False

        return True

# Pydantic models with validation
class TrendInputModel(BaseModel):
    keyword: str
    region: str
    category: str

    @validator('keyword')
    def validate_keyword(cls, v):
        validator = InputValidator()

        # Sanitize input
        v = validator.sanitize_text(v)

        # Validate length
        if not validator.validate_length(v, min_length=2, max_length=100):
            raise ValueError('Keyword must be between 2 and 100 characters')

        # Check for malicious patterns
        if not validator.validate_sql_injection(v):
            raise ValueError('Invalid keyword format')

        if not validator.validate_xss(v):
            raise ValueError('Invalid keyword format')

        return v

    @validator('region')
    def validate_region(cls, v):
        allowed_regions = ['US', 'UK', 'CA', 'AU', 'DE', 'FR', 'JP']
        if v not in allowed_regions:
            raise ValueError(f'Region must be one of: {", ".join(allowed_regions)}')
        return v

    @validator('category')
    def validate_category(cls, v):
        allowed_categories = ['Technology', 'Health', 'Entertainment', 'Sports', 'Business']
        if v not in allowed_categories:
            raise ValueError(f'Category must be one of: {", ".join(allowed_categories)}')
        return v

class ContentInputModel(BaseModel):
    title: str
    description: Optional[str] = None
    body: str

    @validator('title')
    def validate_title(cls, v):
        validator = InputValidator()
        v = validator.sanitize_text(v)

        if not validator.validate_length(v, min_length=5, max_length=200):
            raise ValueError('Title must be between 5 and 200 characters')

        return v

    @validator('body')
    def validate_body(cls, v):
        validator = InputValidator()
        v = validator.sanitize_html(v)

        if not validator.validate_length(v, min_length=100, max_length=10000):
            raise ValueError('Body must be between 100 and 10000 characters')

        return v
```

## Key Components

### Directory Structure
```
security/
├── __init__.py
├── config.py            # Security configuration
├── vault/
│   ├── __init__.py
│   ├── vault_client.py  # HashiCorp Vault integration
│   └── secret_manager.py # Secret management wrapper
├── rotation/
│   ├── __init__.py
│   ├── key_rotator.py   # API key rotation system
│   └── cert_rotator.py  # Certificate rotation
├── middleware/
│   ├── __init__.py
│   ├── security_middleware.py # Security middleware
│   ├── auth_middleware.py     # Authentication middleware
│   └── cors_middleware.py     # CORS configuration
├── validation/
│   ├── __init__.py
│   ├── input_validator.py     # Input validation and sanitization
│   └── schema_validator.py    # API schema validation
├── monitoring/
│   ├── __init__.py
│   ├── security_monitor.py    # Security event monitoring
│   └── threat_detector.py     # Threat detection
├── compliance/
│   ├── __init__.py
│   ├── audit_logger.py        # Compliance audit logging
│   └── gdpr_compliance.py     # GDPR compliance tools
└── utils/
    ├── crypto_utils.py         # Cryptographic utilities
    ├── token_manager.py        # JWT token management
    └── password_policy.py      # Password policy enforcement
```

### Configuration
```python
# security/config.py
SECURITY_CONFIG = {
    'vault': {
        'url': env('VAULT_URL'),
        'auth_method': 'token',
        'token': env('VAULT_TOKEN'),
        'mount_point': 'secret',
        'secret_ttl': 86400  # 24 hours
    },
    'api_key_rotation': {
        'enabled': True,
        'schedule': {
            'openai': 30,      # days
            'cloudflare': 90,
            'github': 60,
            'supabase': 90
        },
        'notification_channels': ['email', 'slack']
    },
    'rate_limiting': {
        'enabled': True,
        'default_limits': {
            'requests': 100,
            'window': 3600  # 1 hour
        },
        'endpoint_limits': {
            'POST:/api/scraper/trigger': {'requests': 10, 'window': 3600},
            'POST:/api/generator/generate': {'requests': 20, 'window': 3600},
            'POST:/api/deploy/trigger': {'requests': 5, 'window': 3600}
        }
    },
    'input_validation': {
        'max_request_size': 10 * 1024 * 1024,  # 10MB
        'allowed_content_types': [
            'application/json',
            'application/x-www-form-urlencoded',
            'multipart/form-data'
        ],
        'sanitization_enabled': True
    },
    'security_headers': {
        'csp_enabled': True,
        'hsts_enabled': True,
        'frame_options': 'DENY',
        'content_type_options': 'nosniff'
    },
    'authentication': {
        'jwt_secret': env('JWT_SECRET'),
        'jwt_expiry': 3600,  # 1 hour
        'refresh_token_expiry': 604800,  # 7 days
        'password_policy': {
            'min_length': 12,
            'require_uppercase': True,
            'require_lowercase': True,
            'require_numbers': True,
            'require_symbols': True
        }
    },
    'monitoring': {
        'security_events_enabled': True,
        'failed_login_threshold': 5,
        'suspicious_activity_threshold': 10,
        'alert_channels': ['email', 'slack', 'pagerduty']
    }
}
```

## API Endpoints

### Security Management API
```python
# security/api.py
from fastapi import APIRouter, Depends, HTTPException
from .vault.vault_client import SecretManager
from .rotation.key_rotator import APIKeyRotator

router = APIRouter(prefix="/api/security")

@router.post("/rotate-keys")
async def rotate_api_keys(
    current_user = Depends(get_current_admin_user)
):
    """Manually trigger API key rotation"""
    rotator = APIKeyRotator()
    results = await rotator.rotate_keys_by_schedule()
    return results

@router.get("/vault/secrets")
async def list_secrets(
    path: str = "",
    current_user = Depends(get_current_admin_user)
):
    """List secrets in Vault"""
    secret_manager = SecretManager()
    secrets = await secret_manager.vault.list_secrets(path)
    return {"secrets": secrets}

@router.get("/security/status")
async def get_security_status():
    """Get overall security status"""
    return {
        "vault_status": "healthy",
        "key_rotation_status": "up_to_date",
        "security_monitoring": "active"
    }
```

## Testing Strategy

### Security Tests
```python
# tests/test_security.py
import pytest
from security.validation.input_validator import InputValidator, TrendInputModel

def test_input_validation():
    validator = InputValidator()

    # Test XSS detection
    malicious_input = "<script>alert('xss')</script>"
    assert not validator.validate_xss(malicious_input)

    # Test SQL injection detection
    sql_injection = "'; DROP TABLE users; --"
    assert not validator.validate_sql_injection(sql_injection)

    # Test valid input
    valid_input = "Machine Learning Trends"
    assert validator.validate_xss(valid_input)
    assert validator.validate_sql_injection(valid_input)

def test_trend_input_model():
    # Valid input
    valid_data = {
        "keyword": "AI Technology",
        "region": "US",
        "category": "Technology"
    }
    trend = TrendInputModel(**valid_data)
    assert trend.keyword == "AI Technology"

    # Invalid input
    with pytest.raises(ValueError):
        TrendInputModel(
            keyword="<script>alert('xss')</script>",
            region="US",
            category="Technology"
        )
```

## Deployment Notes

### Environment Variables
```bash
# Vault Configuration
VAULT_URL=https://vault.yourdomain.com
VAULT_TOKEN=your_vault_token
VAULT_AUTH_METHOD=token

# Security Settings
JWT_SECRET=your_jwt_secret_key
ENCRYPTION_KEY=your_encryption_key
RATE_LIMITING_ENABLED=true

# API Key Rotation
API_KEY_ROTATION_ENABLED=true
KEY_ROTATION_SCHEDULE=monthly

# Monitoring
SECURITY_MONITORING_ENABLED=true
FAILED_LOGIN_THRESHOLD=5
ALERT_EMAIL=<EMAIL>
```

### Vault Setup Commands
```bash
# Initialize Vault
vault operator init
vault operator unseal

# Enable KV secrets engine
vault secrets enable -path=secret kv-v2

# Create policies
vault policy write trend-platform-policy - <<EOF
path "secret/data/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}
EOF

# Create token
vault token create -policy=trend-platform-policy
```

## Success Criteria
- Zero security vulnerabilities in production
- 100% API key rotation compliance
- Sub-100ms security middleware overhead
- Comprehensive audit trail for all security events
- Automated threat detection and response
```