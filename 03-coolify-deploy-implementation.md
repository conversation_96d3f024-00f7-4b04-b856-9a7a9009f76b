# Coolify Deploy Orchestrator - Implementation Plan

## Overview
The Coolify Deploy Orchestrator manages the deployment pipeline from Git repository changes to live websites. It interfaces with Coolify's API to trigger builds, monitor deployment status, and manage the Next.js application lifecycle on OCI infrastructure.

## Dependencies
- **Infrastructure Module**: OCI VM with Coolify installation
- **Site Generator Module**: Receives deployment triggers after content generation
- **Database Module**: Stores deployment status and metadata
- **DNS Module**: Coordinates DNS updates after successful deployments
- **Monitoring Module**: Tracks deployment metrics and performance

## Interfaces
### Outbound
- **Coolify API**: Deployment management and status monitoring
- **Git Repository**: Webhook configuration and repository management
- **DNS Module**: Triggers DNS record creation/updates
- **Database**: Updates deployment status and logs

### Inbound
- **Site Generator**: Deployment trigger after content creation
- **Dashboard**: Manual deployment controls
- **Git Webhooks**: Automatic deployment triggers
- **API**: Deployment status and management endpoints

## Implementation Phases

### Phase 1: Infrastructure Setup
**Duration**: Week 1-2

#### 1.1 OCI VM Provisioning
```bash
# infrastructure/oci-setup.sh
#!/bin/bash

# Create OCI Ampere A1 instance
oci compute instance launch \
  --availability-domain "AD-1" \
  --compartment-id $OCI_COMPARTMENT_ID \
  --image-id $UBUNTU_ARM64_IMAGE_ID \
  --shape "VM.Standard.A1.Flex" \
  --shape-config '{"ocpus": 4, "memory_in_gbs": 24}' \
  --subnet-id $SUBNET_ID \
  --assign-public-ip true \
  --ssh-authorized-keys-file ~/.ssh/id_rsa.pub \
  --display-name "coolify-deploy-server"
```

#### 1.2 Coolify Installation
```bash
# infrastructure/coolify-install.sh
#!/bin/bash

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Install Coolify
curl -fsSL https://cdn.coollabs.io/coolify/install.sh | bash

# Configure SSL with Let's Encrypt
sudo coolify ssl --domain deploy.yourdomain.com --email <EMAIL>
```

#### 1.3 Coolify Configuration
```python
# deploy/coolify_config.py
from typing import Dict, Any

COOLIFY_CONFIG = {
    'server': {
        'url': 'https://deploy.yourdomain.com',
        'api_token': env('COOLIFY_API_TOKEN'),
        'team_id': env('COOLIFY_TEAM_ID')
    },
    'application': {
        'name': 'trend-content-site',
        'git_repository': env('CONTENT_REPO_URL'),
        'branch': 'main',
        'build_command': 'npm run build',
        'start_command': 'npm start',
        'port': 3000,
        'environment': 'production'
    },
    'deployment': {
        'auto_deploy': True,
        'build_timeout': 600,  # 10 minutes
        'health_check_path': '/api/health',
        'health_check_timeout': 30
    }
}
```

### Phase 2: Deploy Pipeline
**Duration**: Week 2-3

#### 2.1 Coolify API Client
```python
# deploy/providers/coolify_provider.py
import aiohttp
import asyncio
from typing import Dict, Any, Optional
from .base_provider import BaseDeployProvider

class CoolifyProvider(BaseDeployProvider):
    def __init__(self, api_url: str, api_token: str, team_id: str):
        self.api_url = api_url.rstrip('/')
        self.api_token = api_token
        self.team_id = team_id
        self.session = aiohttp.ClientSession(
            headers={'Authorization': f'Bearer {api_token}'}
        )
    
    async def create_application(self, config: Dict[str, Any]) -> str:
        """Create new application in Coolify"""
        payload = {
            'name': config['name'],
            'git_repository': config['git_repository'],
            'git_branch': config['branch'],
            'build_command': config['build_command'],
            'start_command': config['start_command'],
            'port': config['port'],
            'environment_variables': config.get('env_vars', {})
        }
        
        async with self.session.post(
            f"{self.api_url}/api/v1/teams/{self.team_id}/applications",
            json=payload
        ) as response:
            data = await response.json()
            return data['uuid']
    
    async def trigger_deployment(self, app_uuid: str, force_rebuild: bool = False) -> str:
        """Trigger deployment for application"""
        payload = {'force_rebuild': force_rebuild}
        
        async with self.session.post(
            f"{self.api_url}/api/v1/applications/{app_uuid}/deploy",
            json=payload
        ) as response:
            data = await response.json()
            return data['deployment_uuid']
    
    async def get_deployment_status(self, deployment_uuid: str) -> Dict[str, Any]:
        """Get deployment status and logs"""
        async with self.session.get(
            f"{self.api_url}/api/v1/deployments/{deployment_uuid}"
        ) as response:
            return await response.json()
    
    async def get_application_info(self, app_uuid: str) -> Dict[str, Any]:
        """Get application information and status"""
        async with self.session.get(
            f"{self.api_url}/api/v1/applications/{app_uuid}"
        ) as response:
            return await response.json()
```

#### 2.2 Deployment Orchestrator
```python
# deploy/orchestrator.py
import asyncio
from typing import Dict, Any, Optional
from .providers.coolify_provider import CoolifyProvider
from .monitoring import DeploymentMonitor
from database.models.deployment_model import DeploymentModel

class DeploymentOrchestrator:
    def __init__(self, provider: CoolifyProvider):
        self.provider = provider
        self.monitor = DeploymentMonitor()
        self.db = DeploymentModel()
    
    async def deploy_content(self, content_id: str, trend_data: Dict) -> str:
        """Deploy content for a specific trend"""
        try:
            # Create deployment record
            deployment_id = await self.db.create_deployment({
                'trend_id': trend_data['id'],
                'content_id': content_id,
                'status': 'pending',
                'triggered_by': 'content_generator'
            })
            
            # Trigger Coolify deployment
            deployment_uuid = await self.provider.trigger_deployment(
                app_uuid=COOLIFY_CONFIG['application']['uuid'],
                force_rebuild=True
            )
            
            # Update deployment record
            await self.db.update_deployment(deployment_id, {
                'coolify_deployment_uuid': deployment_uuid,
                'status': 'building'
            })
            
            # Start monitoring
            asyncio.create_task(
                self.monitor_deployment(deployment_id, deployment_uuid)
            )
            
            return deployment_id
            
        except Exception as e:
            await self.db.update_deployment(deployment_id, {
                'status': 'failed',
                'error_message': str(e)
            })
            raise
    
    async def monitor_deployment(self, deployment_id: str, deployment_uuid: str):
        """Monitor deployment progress and update status"""
        max_wait_time = 600  # 10 minutes
        check_interval = 10  # 10 seconds
        elapsed_time = 0
        
        while elapsed_time < max_wait_time:
            try:
                status_data = await self.provider.get_deployment_status(deployment_uuid)
                
                await self.db.update_deployment(deployment_id, {
                    'status': status_data['status'],
                    'build_log': status_data.get('logs', ''),
                    'progress': status_data.get('progress', 0)
                })
                
                if status_data['status'] in ['success', 'failed']:
                    if status_data['status'] == 'success':
                        await self.handle_successful_deployment(deployment_id)
                    break
                
                await asyncio.sleep(check_interval)
                elapsed_time += check_interval
                
            except Exception as e:
                await self.db.update_deployment(deployment_id, {
                    'status': 'failed',
                    'error_message': f"Monitoring error: {str(e)}"
                })
                break
    
    async def handle_successful_deployment(self, deployment_id: str):
        """Handle post-deployment tasks"""
        deployment = await self.db.get_deployment(deployment_id)
        
        # Get application URL
        app_info = await self.provider.get_application_info(
            COOLIFY_CONFIG['application']['uuid']
        )
        deploy_url = app_info.get('url')
        
        # Update deployment record
        await self.db.update_deployment(deployment_id, {
            'deploy_url': deploy_url,
            'deployed_at': datetime.utcnow()
        })
        
        # Trigger DNS update
        from dns.cloudflare_api import update_dns_record
        await update_dns_record(deployment['trend_id'], deploy_url)
        
        # Notify monitoring system
        self.monitor.log_successful_deployment(deployment_id, deploy_url)
```

### Phase 3: Webhook Management
**Duration**: Week 3

#### 3.1 Git Webhook Handler
```python
# deploy/hooks.py
from fastapi import APIRouter, Request, HTTPException
import hmac
import hashlib
from .orchestrator import DeploymentOrchestrator

router = APIRouter(prefix="/api/deploy/webhooks")

@router.post("/git")
async def handle_git_webhook(request: Request):
    """Handle Git repository webhook for automatic deployments"""
    
    # Verify webhook signature
    signature = request.headers.get('X-Hub-Signature-256')
    if not verify_webhook_signature(await request.body(), signature):
        raise HTTPException(status_code=401, detail="Invalid signature")
    
    payload = await request.json()
    
    # Check if this is a push to main branch
    if (payload.get('ref') == 'refs/heads/main' and 
        payload.get('repository', {}).get('name') == 'content-repo'):
        
        # Extract changed files
        changed_files = []
        for commit in payload.get('commits', []):
            changed_files.extend(commit.get('added', []))
            changed_files.extend(commit.get('modified', []))
        
        # Check if content files were changed
        content_changes = [f for f in changed_files if f.startswith('content/')]
        
        if content_changes:
            # Trigger deployment
            orchestrator = DeploymentOrchestrator()
            deployment_id = await orchestrator.deploy_content(
                content_id=None,  # Auto-deployment
                trend_data={'source': 'git_webhook'}
            )
            
            return {"status": "deployment_triggered", "deployment_id": deployment_id}
    
    return {"status": "no_action_required"}

def verify_webhook_signature(payload: bytes, signature: str) -> bool:
    """Verify GitHub webhook signature"""
    secret = env('GITHUB_WEBHOOK_SECRET').encode()
    expected_signature = 'sha256=' + hmac.new(
        secret, payload, hashlib.sha256
    ).hexdigest()
    return hmac.compare_digest(expected_signature, signature)
```

## Key Components

### Directory Structure
```
deploy/
├── __init__.py
├── orchestrator.py       # Main deployment controller
├── tasks.py             # Celery task definitions
├── config.py            # Configuration management
├── providers/
│   ├── __init__.py
│   ├── base_provider.py # Abstract deployment provider
│   ├── coolify_provider.py # Coolify-specific implementation
│   └── vercel_provider.py  # Alternative provider
├── hooks.py             # Webhook management
├── monitoring.py        # Deployment monitoring
├── health_checks.py     # Application health verification
└── utils/
    ├── ssl_manager.py   # SSL certificate management
    ├── domain_validator.py # Domain validation utilities
    └── build_optimizer.py  # Build optimization helpers
```

### Configuration
```python
# deploy/config.py
DEPLOY_CONFIG = {
    'coolify': {
        'api_url': env('COOLIFY_API_URL'),
        'api_token': env('COOLIFY_API_TOKEN'),
        'team_id': env('COOLIFY_TEAM_ID'),
        'application_uuid': env('COOLIFY_APP_UUID')
    },
    'git': {
        'webhook_secret': env('GITHUB_WEBHOOK_SECRET'),
        'auto_deploy_enabled': True,
        'deploy_branch': 'main'
    },
    'build': {
        'timeout': 600,
        'node_version': '18',
        'build_command': 'npm run build',
        'start_command': 'npm start',
        'install_command': 'npm ci'
    },
    'health_checks': {
        'enabled': True,
        'path': '/api/health',
        'timeout': 30,
        'retries': 3
    }
}
```

## Data Models

### Deployment Data Structure
```python
# deploy/models.py
from pydantic import BaseModel, HttpUrl
from datetime import datetime
from typing import Optional, Dict, Any
from enum import Enum

class DeploymentStatus(str, Enum):
    PENDING = "pending"
    BUILDING = "building"
    SUCCESS = "success"
    FAILED = "failed"
    CANCELLED = "cancelled"

class DeploymentRequest(BaseModel):
    trend_id: str
    content_id: Optional[str]
    force_rebuild: bool = False
    environment: str = "production"
    triggered_by: str

class DeploymentRecord(BaseModel):
    id: str
    trend_id: str
    content_id: Optional[str]
    status: DeploymentStatus
    coolify_deployment_uuid: Optional[str]
    deploy_url: Optional[HttpUrl]
    build_log: Optional[str]
    error_message: Optional[str]
    progress: int = 0
    created_at: datetime
    deployed_at: Optional[datetime]
    build_duration: Optional[int]  # seconds
```

## API Endpoints

### Deployment Management API
```python
# deploy/api.py
from fastapi import APIRouter, Depends, HTTPException
from .tasks import deploy_content_task
from .models import DeploymentRequest

router = APIRouter(prefix="/api/deploy")

@router.post("/trigger")
async def trigger_deployment(
    request: DeploymentRequest,
    current_user = Depends(get_current_user)
):
    """Manually trigger deployment"""
    task = deploy_content_task.delay(request.dict())
    return {"task_id": task.id, "status": "started"}

@router.get("/status/{deployment_id}")
async def get_deployment_status(deployment_id: str):
    """Get deployment status and logs"""
    deployment = await db.get_deployment(deployment_id)
    if not deployment:
        raise HTTPException(status_code=404, detail="Deployment not found")
    return deployment

@router.post("/cancel/{deployment_id}")
async def cancel_deployment(
    deployment_id: str,
    current_user = Depends(get_current_user)
):
    """Cancel ongoing deployment"""
    # Implementation to cancel Coolify deployment
    pass

@router.get("/health/{deployment_id}")
async def check_deployment_health(deployment_id: str):
    """Check health of deployed application"""
    deployment = await db.get_deployment(deployment_id)
    if not deployment or not deployment.deploy_url:
        raise HTTPException(status_code=404, detail="Deployment not found")
    
    health_status = await check_application_health(deployment.deploy_url)
    return health_status
```

## Testing Strategy

### Unit Tests
```python
# tests/test_deployment.py
import pytest
from deploy.providers.coolify_provider import CoolifyProvider
from deploy.orchestrator import DeploymentOrchestrator

@pytest.mark.asyncio
async def test_coolify_deployment():
    provider = MockCoolifyProvider()
    orchestrator = DeploymentOrchestrator(provider)
    
    deployment_id = await orchestrator.deploy_content(
        content_id="test-content-123",
        trend_data={"id": "trend-123", "keyword": "test"}
    )
    
    assert deployment_id
    
    # Wait for deployment to complete
    await asyncio.sleep(5)
    
    deployment = await db.get_deployment(deployment_id)
    assert deployment.status == "success"

@pytest.mark.asyncio
async def test_webhook_handling():
    webhook_payload = {
        "ref": "refs/heads/main",
        "repository": {"name": "content-repo"},
        "commits": [{"added": ["content/new-trend/index.mdx"]}]
    }
    
    response = await handle_git_webhook_test(webhook_payload)
    assert response["status"] == "deployment_triggered"
```

### Integration Tests
```python
# tests/test_integration.py
@pytest.mark.integration
async def test_full_deployment_pipeline():
    # Create test content
    content_id = await create_test_content()
    
    # Trigger deployment
    orchestrator = DeploymentOrchestrator()
    deployment_id = await orchestrator.deploy_content(content_id, test_trend_data)
    
    # Wait for deployment
    deployment = await wait_for_deployment_completion(deployment_id)
    
    # Verify deployment success
    assert deployment.status == "success"
    assert deployment.deploy_url
    
    # Verify application is accessible
    health_check = await check_application_health(deployment.deploy_url)
    assert health_check["status"] == "healthy"
```

## Deployment Notes

### Environment Variables
```bash
# Coolify Configuration
COOLIFY_API_URL=https://deploy.yourdomain.com
COOLIFY_API_TOKEN=your_coolify_api_token
COOLIFY_TEAM_ID=your_team_id
COOLIFY_APP_UUID=your_application_uuid

# Git Configuration
GITHUB_WEBHOOK_SECRET=your_webhook_secret
CONTENT_REPO_URL=https://github.com/user/content-repo.git

# Build Configuration
NODE_VERSION=18
BUILD_TIMEOUT=600
HEALTH_CHECK_TIMEOUT=30
```

### Celery Task Configuration
```python
# deploy/tasks.py
from celery import Celery
from .orchestrator import DeploymentOrchestrator

@celery.task(bind=True, max_retries=3)
def deploy_content_task(self, deployment_request):
    try:
        orchestrator = DeploymentOrchestrator()
        deployment_id = orchestrator.deploy_content(
            deployment_request['content_id'],
            deployment_request['trend_data']
        )
        return {"deployment_id": deployment_id, "status": "success"}
    except Exception as exc:
        self.retry(countdown=60, exc=exc)
```

## Success Criteria
- Deploy content within 3 minutes of generation completion
- Maintain 99% deployment success rate
- Zero downtime during deployments
- Automatic rollback on deployment failures
- Complete deployment monitoring and logging
