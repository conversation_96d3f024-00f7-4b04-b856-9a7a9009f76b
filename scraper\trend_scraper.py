"""
Trend scraping system for collecting trending topics from multiple sources
Supports Google Trends, Trends24, and other trend sources
"""
import asyncio
import aiohttp
import time
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass
from pytrends.request import TrendReq
from shared.config import settings, SCRAPER_CONFIG
from shared.utils import generate_slug, AsyncHTTPClient
from shared.exceptions import ScrapingError
from monitoring.logger import get_logger
from monitoring.metrics import app_metrics
from database.models.trend_model import TrendRepository, TrendCreateRequest

logger = get_logger('scraper')


@dataclass
class TrendData:
    """Data structure for trend information"""
    keyword: str
    region: str
    category: str
    search_volume: Optional[int] = None
    growth_rate: Optional[float] = None
    source: str = ""
    raw_data: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.raw_data is None:
            self.raw_data = {}


class BaseTrendScraper:
    """Base class for trend scrapers"""
    
    def __init__(self, source_name: str):
        self.source_name = source_name
        self.logger = logger
        self.metrics = app_metrics
        self.trend_repo = TrendRepository()
    
    async def scrape_trends(self, region: str, category: str) -> List[TrendData]:
        """Scrape trends for given region and category"""
        raise NotImplementedError("Subclasses must implement scrape_trends")
    
    def calculate_score(self, trend: TrendData) -> float:
        """Calculate trend score based on search volume and growth rate"""
        if not trend.search_volume and not trend.growth_rate:
            return 0.0
        
        # Scoring weights from config
        volume_weight = SCRAPER_CONFIG['scoring']['search_volume_weight']
        growth_weight = SCRAPER_CONFIG['scoring']['growth_rate_weight']
        
        # Normalize search volume (0-100 scale)
        volume_score = min(100, (trend.search_volume or 0) / 1000) if trend.search_volume else 0
        
        # Normalize growth rate (0-100 scale, capped at 500% growth)
        growth_score = min(100, max(0, (trend.growth_rate or 0) / 5)) if trend.growth_rate else 0
        
        # Calculate weighted score
        score = (volume_score * volume_weight) + (growth_score * growth_weight)
        
        return round(score, 2)
    
    async def save_trend(self, trend: TrendData) -> Optional[str]:
        """Save trend to database"""
        try:
            # Check if trend already exists
            existing = await self.trend_repo.get_by_keyword_and_region(
                trend.keyword, trend.region
            )
            
            if existing:
                # Update existing trend with new data
                update_data = {
                    'search_volume': trend.search_volume,
                    'growth_rate': trend.growth_rate,
                    'raw_data': trend.raw_data,
                    'source': trend.source
                }
                
                # Recalculate score
                trend.search_volume = trend.search_volume or existing.search_volume
                trend.growth_rate = trend.growth_rate or existing.growth_rate
                update_data['score'] = self.calculate_score(trend)
                
                await self.trend_repo.update(existing.id, update_data)
                return existing.id
            else:
                # Create new trend
                slug = generate_slug(f"{trend.keyword}-{trend.region}")
                score = self.calculate_score(trend)
                
                trend_request = TrendCreateRequest(
                    keyword=trend.keyword,
                    region=trend.region,
                    category=trend.category,
                    search_volume=trend.search_volume,
                    growth_rate=trend.growth_rate,
                    source=trend.source,
                    raw_data=trend.raw_data
                )
                
                create_data = trend_request.dict()
                create_data.update({
                    'slug': slug,
                    'score': score
                })
                
                return await self.trend_repo.create(create_data)
                
        except Exception as e:
            self.logger.error(f"Failed to save trend: {str(e)}", trend_keyword=trend.keyword)
            raise ScrapingError(f"Failed to save trend: {str(e)}")


class GoogleTrendsScraper(BaseTrendScraper):
    """Google Trends scraper using pytrends"""
    
    def __init__(self):
        super().__init__("google_trends")
        self.pytrends = None
        self.last_request_time = 0
        self.request_delay = 60 / SCRAPER_CONFIG['rate_limiting']['google_trends']['requests_per_minute']
    
    def _init_pytrends(self):
        """Initialize pytrends client"""
        if not self.pytrends:
            self.pytrends = TrendReq(hl='en-US', tz=360)
    
    async def _rate_limit(self):
        """Implement rate limiting for Google Trends"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.request_delay:
            sleep_time = self.request_delay - time_since_last
            await asyncio.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    async def scrape_trends(self, region: str, category: str) -> List[TrendData]:
        """Scrape trending topics from Google Trends"""
        start_time = time.time()
        trends = []
        
        try:
            self._init_pytrends()
            await self._rate_limit()
            
            # Map category to Google Trends category
            category_map = {
                'Technology': 5,
                'Health': 45,
                'Entertainment': 16,
                'Sports': 20,
                'Business': 12
            }
            
            cat_id = category_map.get(category, 0)
            
            # Get trending searches
            trending_searches = self.pytrends.trending_searches(pn=region.lower())
            
            if trending_searches is not None and not trending_searches.empty:
                for keyword in trending_searches[0].head(20):  # Top 20 trends
                    await self._rate_limit()
                    
                    try:
                        # Get interest over time for the keyword
                        self.pytrends.build_payload([keyword], cat=cat_id, timeframe='now 7-d', geo=region)
                        interest_data = self.pytrends.interest_over_time()
                        
                        if not interest_data.empty:
                            # Calculate metrics
                            values = interest_data[keyword].values
                            current_volume = int(values[-1]) if len(values) > 0 else 0
                            
                            # Calculate growth rate (last 7 days)
                            if len(values) >= 7:
                                week_ago = values[-7]
                                growth_rate = ((current_volume - week_ago) / max(week_ago, 1)) * 100
                            else:
                                growth_rate = 0.0
                            
                            # Get related queries for additional context
                            related_queries = self.pytrends.related_queries()
                            
                            trend = TrendData(
                                keyword=keyword,
                                region=region,
                                category=category,
                                search_volume=current_volume,
                                growth_rate=growth_rate,
                                source=self.source_name,
                                raw_data={
                                    'interest_over_time': interest_data.to_dict(),
                                    'related_queries': related_queries,
                                    'category_id': cat_id,
                                    'timeframe': 'now 7-d'
                                }
                            )
                            
                            trends.append(trend)
                            
                    except Exception as e:
                        self.logger.warning(f"Failed to get data for keyword '{keyword}': {str(e)}")
                        continue
            
            duration = time.time() - start_time
            
            # Record metrics
            self.metrics.record_trend_scraped(self.source_name, region, category, len(trends))
            self.metrics.record_scraping_duration(self.source_name, duration)
            
            self.logger.info(
                f"Scraped {len(trends)} trends from Google Trends",
                source=self.source_name,
                region=region,
                category=category,
                duration=duration
            )
            
            return trends
            
        except Exception as e:
            duration = time.time() - start_time
            self.metrics.record_scraping_error(self.source_name, "google_trends_error")
            self.logger.error(
                f"Google Trends scraping failed: {str(e)}",
                source=self.source_name,
                region=region,
                category=category,
                duration=duration
            )
            raise ScrapingError(f"Google Trends scraping failed: {str(e)}")


class Trends24Scraper(BaseTrendScraper):
    """Trends24 scraper for real-time trending topics"""
    
    def __init__(self):
        super().__init__("trends24")
        self.base_url = "https://trends24.in"
        self.request_delay = 60 / SCRAPER_CONFIG['rate_limiting']['trends24']['requests_per_minute']
        self.last_request_time = 0
    
    async def _rate_limit(self):
        """Implement rate limiting for Trends24"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.request_delay:
            sleep_time = self.request_delay - time_since_last
            await asyncio.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    async def scrape_trends(self, region: str, category: str) -> List[TrendData]:
        """Scrape trending topics from Trends24"""
        start_time = time.time()
        trends = []
        
        try:
            await self._rate_limit()
            
            # Map region to Trends24 country code
            region_map = {
                'US': 'united-states',
                'UK': 'united-kingdom',
                'CA': 'canada',
                'AU': 'australia',
                'DE': 'germany',
                'FR': 'france',
                'JP': 'japan'
            }
            
            country_code = region_map.get(region, 'united-states')
            url = f"{self.base_url}/{country_code}/"
            
            async with AsyncHTTPClient() as client:
                response = await client.get(url, headers={
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                })
                
                if response['status'] != 200:
                    raise ScrapingError(f"Trends24 API returned status {response['status']}")
                
                # Parse HTML response (simplified - would need proper HTML parsing)
                html_content = response['data']
                
                # Extract trending topics (this is a simplified example)
                # In a real implementation, you'd use BeautifulSoup or similar
                import re
                trend_pattern = r'<a[^>]*class="[^"]*trend[^"]*"[^>]*>([^<]+)</a>'
                matches = re.findall(trend_pattern, html_content, re.IGNORECASE)
                
                for i, keyword in enumerate(matches[:20]):  # Top 20 trends
                    # Estimate metrics based on position
                    search_volume = max(100 - (i * 5), 10)  # Decreasing volume by position
                    growth_rate = max(50 - (i * 2), 5)     # Decreasing growth by position
                    
                    trend = TrendData(
                        keyword=keyword.strip(),
                        region=region,
                        category=category,
                        search_volume=search_volume,
                        growth_rate=growth_rate,
                        source=self.source_name,
                        raw_data={
                            'position': i + 1,
                            'url': url,
                            'scraped_at': datetime.utcnow().isoformat()
                        }
                    )
                    
                    trends.append(trend)
            
            duration = time.time() - start_time
            
            # Record metrics
            self.metrics.record_trend_scraped(self.source_name, region, category, len(trends))
            self.metrics.record_scraping_duration(self.source_name, duration)
            
            self.logger.info(
                f"Scraped {len(trends)} trends from Trends24",
                source=self.source_name,
                region=region,
                category=category,
                duration=duration
            )
            
            return trends
            
        except Exception as e:
            duration = time.time() - start_time
            self.metrics.record_scraping_error(self.source_name, "trends24_error")
            self.logger.error(
                f"Trends24 scraping failed: {str(e)}",
                source=self.source_name,
                region=region,
                category=category,
                duration=duration
            )
            raise ScrapingError(f"Trends24 scraping failed: {str(e)}")


class TrendScrapingOrchestrator:
    """Orchestrates trend scraping from multiple sources"""
    
    def __init__(self):
        self.scrapers = {
            'google_trends': GoogleTrendsScraper(),
            'trends24': Trends24Scraper()
        }
        self.logger = logger
        self.metrics = app_metrics
        self.trend_repo = TrendRepository()
    
    async def scrape_all_trends(self) -> Dict[str, Any]:
        """Scrape trends from all sources and regions"""
        start_time = time.time()
        results = {
            'total_scraped': 0,
            'total_saved': 0,
            'sources': {},
            'errors': []
        }
        
        regions = SCRAPER_CONFIG['regions']
        categories = SCRAPER_CONFIG['categories']
        
        self.logger.info("Starting comprehensive trend scraping", regions=regions, categories=categories)
        
        for source_name, scraper in self.scrapers.items():
            source_results = {
                'scraped': 0,
                'saved': 0,
                'errors': []
            }
            
            for region in regions:
                for category in categories:
                    try:
                        # Scrape trends
                        trends = await scraper.scrape_trends(region, category)
                        source_results['scraped'] += len(trends)
                        
                        # Save trends to database
                        for trend in trends:
                            try:
                                # Only save trends with minimum score
                                score = scraper.calculate_score(trend)
                                if score >= SCRAPER_CONFIG['scoring']['minimum_score']:
                                    trend_id = await scraper.save_trend(trend)
                                    if trend_id:
                                        source_results['saved'] += 1
                                        
                                        self.logger.debug(
                                            f"Saved trend: {trend.keyword}",
                                            trend_id=trend_id,
                                            keyword=trend.keyword,
                                            score=score,
                                            region=region,
                                            category=category
                                        )
                                
                            except Exception as e:
                                error_msg = f"Failed to save trend '{trend.keyword}': {str(e)}"
                                source_results['errors'].append(error_msg)
                                self.logger.error(error_msg)
                        
                        # Small delay between region/category combinations
                        await asyncio.sleep(1)
                        
                    except Exception as e:
                        error_msg = f"Failed to scrape {source_name} for {region}/{category}: {str(e)}"
                        source_results['errors'].append(error_msg)
                        self.logger.error(error_msg)
            
            results['sources'][source_name] = source_results
            results['total_scraped'] += source_results['scraped']
            results['total_saved'] += source_results['saved']
            results['errors'].extend(source_results['errors'])
        
        duration = time.time() - start_time
        
        self.logger.info(
            "Trend scraping completed",
            total_scraped=results['total_scraped'],
            total_saved=results['total_saved'],
            duration=duration,
            error_count=len(results['errors'])
        )
        
        return results
    
    async def get_scraping_statistics(self) -> Dict[str, Any]:
        """Get scraping statistics"""
        stats = await self.trend_repo.get_statistics()
        
        return {
            'database_stats': stats,
            'scraper_config': SCRAPER_CONFIG,
            'available_sources': list(self.scrapers.keys()),
            'supported_regions': SCRAPER_CONFIG['regions'],
            'supported_categories': SCRAPER_CONFIG['categories']
        }


# Global orchestrator instance
trend_orchestrator = TrendScrapingOrchestrator()
