# Core web framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Database
asyncpg==0.29.0
supabase==2.3.0

# Redis and Celery
redis==5.0.1
celery==5.3.4
celery[redis]==5.3.4

# HTTP clients
aiohttp==3.9.1
httpx==0.25.2
requests==2.31.0

# Authentication and Security
PyJWT==2.8.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
cryptography==41.0.8

# Monitoring and Metrics
prometheus-client==0.19.0
structlog==23.2.0

# AI and Content Generation
openai==1.3.7
pytrends==4.9.2

# Web scraping
beautifulsoup4==4.12.2
lxml==4.9.3
selenium==4.16.0

# Utilities
python-slugify==8.0.1
python-dateutil==2.8.2
pytz==2023.3
click==8.1.7

# Development and Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
black==23.11.0
flake8==6.1.0
mypy==1.7.1

# Environment management
python-dotenv==1.0.0

# File handling
Pillow==10.1.0

# DNS and networking
dnspython==2.4.2
cloudflare==2.11.7

# Template engine
Jinja2==3.1.2

# Data validation and serialization
marshmallow==3.20.1

# Async utilities
asyncio-mqtt==0.16.1

# Configuration
PyYAML==6.0.1
toml==0.10.2

# Logging
loguru==0.7.2

# Performance
orjson==3.9.10

# Development server
watchfiles==0.21.0

# Documentation
mkdocs==1.5.3
mkdocs-material==9.4.8
