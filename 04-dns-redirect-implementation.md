# DNS & Redirect Layer - Implementation Plan

## Overview
The DNS & Redirect Layer manages dynamic domain routing and DNS record automation using Cloudflare Workers and API. It handles wildcard CNAME redirects, automated DNS record creation/deletion, and provides caching for optimal performance.

## Dependencies
- **Coolify Deploy Module**: Receives deployment URLs for DNS configuration
- **Database Module**: Stores DNS record mappings and status
- **House-keeping Module**: Coordinates DNS cleanup for expired content
- **Security Module**: Cloudflare API key management
- **Monitoring Module**: DNS performance and error tracking

## Interfaces
### Outbound
- **Cloudflare API**: DNS record management and zone configuration
- **Cloudflare Workers**: Wildcard redirect logic execution
- **Database**: DNS record storage and mapping persistence

### Inbound
- **Coolify Deploy**: DNS update triggers after successful deployments
- **House-keeping**: DNS cleanup requests for expired content
- **Dashboard**: Manual DNS management controls
- **External Traffic**: Incoming requests to trend domains

## Implementation Phases

### Phase 1: Cloudflare Worker Implementation
**Duration**: Week 3-4

#### 1.1 Wildcard Redirect Worker
```javascript
// worker/redirect.js
export default {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);
    const hostname = url.hostname;
    
    // Extract trend slug from subdomain
    const trendSlug = extractTrendSlug(hostname);
    
    if (!trendSlug) {
      return new Response('Invalid domain format', { status: 400 });
    }
    
    try {
      // Check if trend exists and get redirect URL
      const redirectUrl = await getTrendRedirectUrl(trendSlug, env);
      
      if (!redirectUrl) {
        return new Response('Trend not found', { status: 404 });
      }
      
      // Perform redirect with caching headers
      return Response.redirect(redirectUrl, 302, {
        headers: {
          'Cache-Control': 'public, max-age=3600', // 1 hour cache
          'X-Trend-Slug': trendSlug,
          'X-Redirect-Source': 'cloudflare-worker'
        }
      });
      
    } catch (error) {
      console.error('Redirect error:', error);
      
      // Log error to analytics
      await logRedirectError(trendSlug, error.message, env);
      
      return new Response('Internal server error', { status: 500 });
    }
  }
};

function extractTrendSlug(hostname) {
  // Extract slug from hostname pattern: {slug}.trends.yourdomain.com
  const parts = hostname.split('.');
  
  if (parts.length >= 3 && parts[1] === 'trends') {
    return parts[0];
  }
  
  return null;
}

async function getTrendRedirectUrl(trendSlug, env) {
  // Check KV store first (cache)
  const cached = await env.TREND_REDIRECTS.get(trendSlug);
  if (cached) {
    const data = JSON.parse(cached);
    
    // Check if not expired
    if (new Date(data.expiresAt) > new Date()) {
      return data.redirectUrl;
    }
  }
  
  // Fetch from database API
  const response = await fetch(`${env.API_BASE_URL}/api/dns/resolve/${trendSlug}`, {
    headers: {
      'Authorization': `Bearer ${env.API_TOKEN}`,
      'Content-Type': 'application/json'
    }
  });
  
  if (!response.ok) {
    return null;
  }
  
  const data = await response.json();
  
  // Cache the result
  await env.TREND_REDIRECTS.put(trendSlug, JSON.stringify({
    redirectUrl: data.redirectUrl,
    expiresAt: data.expiresAt,
    cachedAt: new Date().toISOString()
  }), {
    expirationTtl: 3600 // 1 hour
  });
  
  return data.redirectUrl;
}

async function logRedirectError(trendSlug, errorMessage, env) {
  try {
    await fetch(`${env.API_BASE_URL}/api/analytics/redirect-error`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${env.API_TOKEN}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        trendSlug,
        errorMessage,
        timestamp: new Date().toISOString(),
        userAgent: request.headers.get('User-Agent'),
        ip: request.headers.get('CF-Connecting-IP')
      })
    });
  } catch (e) {
    console.error('Failed to log redirect error:', e);
  }
}
```

#### 1.2 Worker Deployment Configuration
```toml
# wrangler.toml
name = "trend-redirect-worker"
main = "src/redirect.js"
compatibility_date = "2024-01-01"

[env.production]
route = "*.trends.yourdomain.com/*"
zone_name = "yourdomain.com"

[[env.production.kv_namespaces]]
binding = "TREND_REDIRECTS"
id = "your_kv_namespace_id"

[env.production.vars]
API_BASE_URL = "https://api.yourdomain.com"
API_TOKEN = "your_api_token"
```

### Phase 2: DNS Automation
**Duration**: Week 4

#### 2.1 Cloudflare API Client
```python
# dns/cloudflare_api.py
import aiohttp
import asyncio
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

@dataclass
class DNSRecord:
    id: Optional[str]
    name: str
    type: str
    content: str
    ttl: int = 1
    proxied: bool = True

class CloudflareAPI:
    def __init__(self, api_token: str, zone_id: str):
        self.api_token = api_token
        self.zone_id = zone_id
        self.base_url = "https://api.cloudflare.com/client/v4"
        self.session = aiohttp.ClientSession(
            headers={
                'Authorization': f'Bearer {api_token}',
                'Content-Type': 'application/json'
            }
        )
    
    async def create_dns_record(self, record: DNSRecord) -> str:
        """Create a new DNS record"""
        payload = {
            'type': record.type,
            'name': record.name,
            'content': record.content,
            'ttl': record.ttl,
            'proxied': record.proxied
        }
        
        async with self.session.post(
            f"{self.base_url}/zones/{self.zone_id}/dns_records",
            json=payload
        ) as response:
            data = await response.json()
            
            if not data['success']:
                raise Exception(f"Failed to create DNS record: {data['errors']}")
            
            return data['result']['id']
    
    async def update_dns_record(self, record_id: str, record: DNSRecord) -> bool:
        """Update existing DNS record"""
        payload = {
            'type': record.type,
            'name': record.name,
            'content': record.content,
            'ttl': record.ttl,
            'proxied': record.proxied
        }
        
        async with self.session.put(
            f"{self.base_url}/zones/{self.zone_id}/dns_records/{record_id}",
            json=payload
        ) as response:
            data = await response.json()
            return data['success']
    
    async def delete_dns_record(self, record_id: str) -> bool:
        """Delete DNS record"""
        async with self.session.delete(
            f"{self.base_url}/zones/{self.zone_id}/dns_records/{record_id}"
        ) as response:
            data = await response.json()
            return data['success']
    
    async def list_dns_records(self, name_filter: str = None) -> List[Dict]:
        """List DNS records with optional name filter"""
        params = {}
        if name_filter:
            params['name'] = name_filter
        
        async with self.session.get(
            f"{self.base_url}/zones/{self.zone_id}/dns_records",
            params=params
        ) as response:
            data = await response.json()
            
            if data['success']:
                return data['result']
            return []
    
    async def purge_cache(self, urls: List[str] = None) -> bool:
        """Purge Cloudflare cache for specific URLs or everything"""
        payload = {}
        if urls:
            payload['files'] = urls
        else:
            payload['purge_everything'] = True
        
        async with self.session.post(
            f"{self.base_url}/zones/{self.zone_id}/purge_cache",
            json=payload
        ) as response:
            data = await response.json()
            return data['success']
```

#### 2.2 DNS Management Service
```python
# dns/dns_manager.py
from typing import Dict, Optional
from .cloudflare_api import CloudflareAPI, DNSRecord
from database.models.dns_model import DNSModel

class DNSManager:
    def __init__(self, cloudflare_api: CloudflareAPI):
        self.cf_api = cloudflare_api
        self.db = DNSModel()
    
    async def create_trend_dns(self, trend_id: str, slug: str, target_url: str) -> str:
        """Create DNS record for a new trend"""
        try:
            # Create subdomain: {slug}.trends.yourdomain.com
            subdomain = f"{slug}.trends.yourdomain.com"
            
            # Create CNAME record pointing to main domain
            dns_record = DNSRecord(
                name=subdomain,
                type='CNAME',
                content='trends.yourdomain.com',
                ttl=1,  # Auto TTL
                proxied=True  # Enable Cloudflare proxy
            )
            
            # Create record in Cloudflare
            record_id = await self.cf_api.create_dns_record(dns_record)
            
            # Store in database
            dns_id = await self.db.create_dns_record({
                'trend_id': trend_id,
                'subdomain': subdomain,
                'target_url': target_url,
                'cloudflare_record_id': record_id,
                'status': 'active',
                'created_at': datetime.utcnow()
            })
            
            # Update KV store for worker
            await self.update_worker_cache(slug, target_url, trend_id)
            
            return dns_id
            
        except Exception as e:
            await self.db.log_dns_error(trend_id, f"DNS creation failed: {str(e)}")
            raise
    
    async def update_trend_dns(self, trend_id: str, new_target_url: str) -> bool:
        """Update DNS record target URL"""
        try:
            dns_record = await self.db.get_dns_by_trend_id(trend_id)
            if not dns_record:
                raise Exception(f"DNS record not found for trend {trend_id}")
            
            # Update database
            await self.db.update_dns_record(dns_record['id'], {
                'target_url': new_target_url,
                'updated_at': datetime.utcnow()
            })
            
            # Update worker cache
            slug = dns_record['subdomain'].split('.')[0]
            await self.update_worker_cache(slug, new_target_url, trend_id)
            
            return True
            
        except Exception as e:
            await self.db.log_dns_error(trend_id, f"DNS update failed: {str(e)}")
            raise
    
    async def delete_trend_dns(self, trend_id: str) -> bool:
        """Delete DNS record for expired trend"""
        try:
            dns_record = await self.db.get_dns_by_trend_id(trend_id)
            if not dns_record:
                return True  # Already deleted
            
            # Delete from Cloudflare
            if dns_record['cloudflare_record_id']:
                await self.cf_api.delete_dns_record(dns_record['cloudflare_record_id'])
            
            # Remove from worker cache
            slug = dns_record['subdomain'].split('.')[0]
            await self.remove_worker_cache(slug)
            
            # Mark as deleted in database
            await self.db.update_dns_record(dns_record['id'], {
                'status': 'deleted',
                'deleted_at': datetime.utcnow()
            })
            
            return True
            
        except Exception as e:
            await self.db.log_dns_error(trend_id, f"DNS deletion failed: {str(e)}")
            raise
    
    async def update_worker_cache(self, slug: str, target_url: str, trend_id: str):
        """Update Cloudflare KV store for worker"""
        # This would typically use Cloudflare KV API
        # For now, we'll use the main API to update cache
        cache_data = {
            'redirectUrl': target_url,
            'trendId': trend_id,
            'expiresAt': (datetime.utcnow() + timedelta(days=14)).isoformat(),
            'updatedAt': datetime.utcnow().isoformat()
        }
        
        # Update via API call to worker or KV directly
        # Implementation depends on your KV access method
        pass
```

## Key Components

### Directory Structure
```
dns/
├── __init__.py
├── cloudflare_api.py     # Cloudflare API client
├── dns_manager.py        # DNS management service
├── tasks.py             # Celery task definitions
├── config.py            # Configuration management
├── monitoring.py        # DNS performance monitoring
├── cache_manager.py     # Worker cache management
└── utils/
    ├── domain_validator.py # Domain validation utilities
    ├── ssl_checker.py      # SSL certificate validation
    └── dns_propagation.py  # DNS propagation checking

worker/
├── redirect.js          # Cloudflare Worker script
├── wrangler.toml       # Worker configuration
└── package.json        # Worker dependencies
```

### Configuration
```python
# dns/config.py
DNS_CONFIG = {
    'cloudflare': {
        'api_token': env('CLOUDFLARE_API_TOKEN'),
        'zone_id': env('CLOUDFLARE_ZONE_ID'),
        'zone_name': 'yourdomain.com',
        'worker_name': 'trend-redirect-worker'
    },
    'domains': {
        'main_domain': 'yourdomain.com',
        'trends_subdomain': 'trends.yourdomain.com',
        'pattern': '{slug}.trends.yourdomain.com'
    },
    'caching': {
        'ttl': 3600,  # 1 hour
        'worker_cache_ttl': 3600,
        'purge_on_update': True
    },
    'monitoring': {
        'health_check_interval': 300,  # 5 minutes
        'dns_propagation_timeout': 300,
        'ssl_check_enabled': True
    }
}
```

## Data Models

### DNS Record Structure
```python
# dns/models.py
from pydantic import BaseModel, HttpUrl
from datetime import datetime
from typing import Optional
from enum import Enum

class DNSStatus(str, Enum):
    PENDING = "pending"
    ACTIVE = "active"
    UPDATING = "updating"
    DELETED = "deleted"
    ERROR = "error"

class DNSRecordCreate(BaseModel):
    trend_id: str
    slug: str
    target_url: HttpUrl
    subdomain: Optional[str] = None

class DNSRecordResponse(BaseModel):
    id: str
    trend_id: str
    subdomain: str
    target_url: HttpUrl
    cloudflare_record_id: Optional[str]
    status: DNSStatus
    created_at: datetime
    updated_at: Optional[datetime]
    deleted_at: Optional[datetime]

class RedirectAnalytics(BaseModel):
    trend_slug: str
    redirect_count: int
    error_count: int
    last_accessed: datetime
    average_response_time: float
```

## API Endpoints

### DNS Management API
```python
# dns/api.py
from fastapi import APIRouter, Depends, HTTPException
from .dns_manager import DNSManager
from .models import DNSRecordCreate

router = APIRouter(prefix="/api/dns")

@router.post("/create")
async def create_dns_record(
    request: DNSRecordCreate,
    current_user = Depends(get_current_user)
):
    """Create DNS record for trend"""
    dns_manager = DNSManager()
    dns_id = await dns_manager.create_trend_dns(
        request.trend_id,
        request.slug,
        str(request.target_url)
    )
    return {"dns_id": dns_id, "status": "created"}

@router.get("/resolve/{slug}")
async def resolve_trend_slug(slug: str):
    """Resolve trend slug to target URL (used by worker)"""
    dns_manager = DNSManager()
    dns_record = await dns_manager.db.get_dns_by_slug(slug)
    
    if not dns_record or dns_record['status'] != 'active':
        raise HTTPException(status_code=404, detail="Trend not found")
    
    return {
        "redirectUrl": dns_record['target_url'],
        "trendId": dns_record['trend_id'],
        "expiresAt": dns_record.get('expires_at')
    }

@router.delete("/{dns_id}")
async def delete_dns_record(
    dns_id: str,
    current_user = Depends(get_current_user)
):
    """Delete DNS record"""
    dns_manager = DNSManager()
    success = await dns_manager.delete_trend_dns(dns_id)
    return {"success": success}

@router.get("/analytics/{slug}")
async def get_redirect_analytics(slug: str):
    """Get redirect analytics for trend"""
    # Implementation to fetch analytics data
    pass
```

## Testing Strategy

### Unit Tests
```python
# tests/test_dns.py
import pytest
from dns.cloudflare_api import CloudflareAPI, DNSRecord
from dns.dns_manager import DNSManager

@pytest.mark.asyncio
async def test_dns_record_creation():
    cf_api = MockCloudflareAPI()
    dns_manager = DNSManager(cf_api)
    
    dns_id = await dns_manager.create_trend_dns(
        trend_id="test-trend-123",
        slug="ai-technology",
        target_url="https://deploy.example.com/ai-technology"
    )
    
    assert dns_id
    
    # Verify record was created
    dns_record = await dns_manager.db.get_dns_record(dns_id)
    assert dns_record['subdomain'] == "ai-technology.trends.yourdomain.com"

@pytest.mark.asyncio
async def test_worker_redirect():
    # Test worker redirect logic
    request = MockRequest("https://ai-technology.trends.yourdomain.com/")
    response = await worker_fetch(request, mock_env)
    
    assert response.status == 302
    assert "deploy.example.com" in response.headers['Location']
```

### Integration Tests
```python
# tests/test_integration.py
@pytest.mark.integration
async def test_full_dns_pipeline():
    # Create trend and content
    trend_id = await create_test_trend()
    content_id = await create_test_content(trend_id)
    
    # Deploy content
    deployment_id = await deploy_content(content_id)
    deployment = await wait_for_deployment(deployment_id)
    
    # Create DNS record
    dns_manager = DNSManager()
    dns_id = await dns_manager.create_trend_dns(
        trend_id, "test-trend", deployment.deploy_url
    )
    
    # Test redirect
    response = await test_redirect("test-trend.trends.yourdomain.com")
    assert response.status_code == 302
```

## Deployment Notes

### Environment Variables
```bash
# Cloudflare Configuration
CLOUDFLARE_API_TOKEN=your_cloudflare_api_token
CLOUDFLARE_ZONE_ID=your_zone_id
CLOUDFLARE_ZONE_NAME=yourdomain.com

# Worker Configuration
WORKER_KV_NAMESPACE_ID=your_kv_namespace_id
WORKER_API_TOKEN=your_worker_api_token

# Domain Configuration
MAIN_DOMAIN=yourdomain.com
TRENDS_SUBDOMAIN=trends.yourdomain.com
```

### Worker Deployment
```bash
# Deploy Cloudflare Worker
npm install -g wrangler
wrangler login
wrangler deploy --env production
```

## Success Criteria
- DNS records created within 30 seconds of deployment
- 99.9% redirect success rate
- Average redirect response time < 100ms
- Zero DNS propagation issues
- Automatic cleanup of expired DNS records
