# Dashboard & Control Module - Implementation Plan

## Overview
The Dashboard & Control Module provides a comprehensive web interface for system management, analytics visualization, and manual control of all platform functions. Built with Next.js and TypeScript, it offers both operational controls and business intelligence dashboards.

## Dependencies
- **Database Module**: Real-time data access for all system entities
- **Security Module**: Authentication, authorization, and session management
- **All System Modules**: Control interfaces and status monitoring
- **Monitoring Module**: Performance metrics and system health data

## Interfaces
### Outbound
- **All Module APIs**: Control commands and status queries
- **Database**: Direct data access for analytics and reporting
- **Supabase Auth**: User authentication and session management

### Inbound
- **Users**: Web interface access via browser
- **API Clients**: RESTful API endpoints for external integrations
- **WebSocket**: Real-time updates and notifications

## Implementation Phases

### Phase 1: Authentication & Core Infrastructure
**Duration**: Week 5-6

#### 1.1 Supabase Auth Integration
```typescript
// dashboard/lib/auth.ts
import { createClient } from '@supabase/supabase-js'
import { NextAuthOptions } from 'next-auth'
import { SupabaseAdapter } from '@next-auth/supabase-adapter'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export const authOptions: NextAuthOptions = {
  adapter: SupabaseAdapter({
    url: process.env.NEXT_PUBLIC_SUPABASE_URL!,
    secret: process.env.SUPABASE_SERVICE_ROLE_KEY!,
  }),
  providers: [
    {
      id: 'supabase',
      name: 'Supabase',
      type: 'oauth',
      // Supabase OAuth configuration
    }
  ],
  callbacks: {
    async session({ session, token }) {
      // Add user role and permissions to session
      const { data: profile } = await supabase
        .from('user_profiles')
        .select('role, permissions')
        .eq('id', token.sub)
        .single()
      
      session.user.role = profile?.role || 'viewer'
      session.user.permissions = profile?.permissions || []
      
      return session
    }
  }
}

// Role-based access control
export const hasPermission = (user: any, permission: string): boolean => {
  return user?.permissions?.includes(permission) || user?.role === 'admin'
}

export const requireAuth = (permission?: string) => {
  return (req: any, res: any, next: any) => {
    if (!req.session?.user) {
      return res.status(401).json({ error: 'Unauthorized' })
    }
    
    if (permission && !hasPermission(req.session.user, permission)) {
      return res.status(403).json({ error: 'Forbidden' })
    }
    
    next()
  }
}
```

#### 1.2 Core Layout & Navigation
```typescript
// dashboard/components/layout/DashboardLayout.tsx
import { useState } from 'react'
import { useSession } from 'next-auth/react'
import Sidebar from './Sidebar'
import Header from './Header'
import { Toaster } from 'react-hot-toast'

interface DashboardLayoutProps {
  children: React.ReactNode
  title?: string
}

export default function DashboardLayout({ children, title }: DashboardLayoutProps) {
  const { data: session, status } = useSession()
  const [sidebarOpen, setSidebarOpen] = useState(false)

  if (status === 'loading') {
    return <div className="flex items-center justify-center h-screen">Loading...</div>
  }

  if (!session) {
    return <div>Please sign in to access the dashboard</div>
  }

  return (
    <div className="h-screen flex overflow-hidden bg-gray-100">
      <Sidebar open={sidebarOpen} setOpen={setSidebarOpen} />
      
      <div className="flex flex-col w-0 flex-1 overflow-hidden">
        <Header 
          title={title} 
          user={session.user} 
          setSidebarOpen={setSidebarOpen} 
        />
        
        <main className="flex-1 relative overflow-y-auto focus:outline-none">
          <div className="py-6">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
              {children}
            </div>
          </div>
        </main>
      </div>
      
      <Toaster position="top-right" />
    </div>
  )
}
```

### Phase 2: Control Panel Dashboard
**Duration**: Week 6-7

#### 2.1 System Control Interface
```typescript
// dashboard/components/controls/SystemControls.tsx
import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useSystemStatus } from '@/hooks/useSystemStatus'
import { useTriggerFunction } from '@/hooks/useTriggerFunction'

export default function SystemControls() {
  const { status, isLoading, refresh } = useSystemStatus()
  const { triggerFunction, isTriggering } = useTriggerFunction()

  const handleTrigger = async (functionName: string) => {
    try {
      await triggerFunction(functionName)
      toast.success(`${functionName} triggered successfully`)
      refresh()
    } catch (error) {
      toast.error(`Failed to trigger ${functionName}`)
    }
  }

  const controlSections = [
    {
      title: 'Data Collection',
      functions: [
        { name: 'scrape-trends', label: 'Scrape Trends', status: status?.scraper },
        { name: 'validate-trends', label: 'Validate Trends', status: status?.validator }
      ]
    },
    {
      title: 'Content Generation',
      functions: [
        { name: 'generate-content', label: 'Generate Content', status: status?.generator },
        { name: 'optimize-images', label: 'Optimize Images', status: status?.optimizer }
      ]
    },
    {
      title: 'Deployment',
      functions: [
        { name: 'deploy-content', label: 'Deploy Content', status: status?.deployer },
        { name: 'update-dns', label: 'Update DNS', status: status?.dns }
      ]
    },
    {
      title: 'Maintenance',
      functions: [
        { name: 'cleanup-expired', label: 'Cleanup Expired', status: status?.cleanup },
        { name: 'sync-analytics', label: 'Sync Analytics', status: status?.analytics }
      ]
    }
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {controlSections.map((section) => (
        <Card key={section.title}>
          <CardHeader>
            <CardTitle>{section.title}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {section.functions.map((func) => (
              <div key={func.name} className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <span className="font-medium">{func.label}</span>
                  <Badge 
                    variant={func.status === 'healthy' ? 'success' : 'destructive'}
                  >
                    {func.status || 'unknown'}
                  </Badge>
                </div>
                <Button
                  size="sm"
                  onClick={() => handleTrigger(func.name)}
                  disabled={isTriggering[func.name]}
                >
                  {isTriggering[func.name] ? 'Running...' : 'Trigger'}
                </Button>
              </div>
            ))}
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
```

#### 2.2 Real-time Operation Logs
```typescript
// dashboard/components/controls/OperationLogs.tsx
import { useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { useWebSocket } from '@/hooks/useWebSocket'

interface LogEntry {
  id: string
  timestamp: string
  level: 'info' | 'warning' | 'error' | 'success'
  module: string
  message: string
  metadata?: any
}

export default function OperationLogs() {
  const [logs, setLogs] = useState<LogEntry[]>([])
  const [filter, setFilter] = useState<string>('all')

  const { lastMessage } = useWebSocket('/api/ws/logs')

  useEffect(() => {
    if (lastMessage) {
      const newLog = JSON.parse(lastMessage.data) as LogEntry
      setLogs(prev => [newLog, ...prev.slice(0, 99)]) // Keep last 100 logs
    }
  }, [lastMessage])

  const filteredLogs = logs.filter(log => 
    filter === 'all' || log.level === filter || log.module === filter
  )

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'error': return 'destructive'
      case 'warning': return 'warning'
      case 'success': return 'success'
      default: return 'secondary'
    }
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Operation Logs</CardTitle>
          <select 
            value={filter} 
            onChange={(e) => setFilter(e.target.value)}
            className="px-3 py-1 border rounded-md"
          >
            <option value="all">All Logs</option>
            <option value="error">Errors</option>
            <option value="warning">Warnings</option>
            <option value="scraper">Scraper</option>
            <option value="generator">Generator</option>
            <option value="deployer">Deployer</option>
          </select>
        </div>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-96">
          <div className="space-y-2">
            {filteredLogs.map((log) => (
              <div key={log.id} className="flex items-start space-x-3 p-3 border rounded-lg">
                <Badge variant={getLevelColor(log.level)}>
                  {log.level}
                </Badge>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium">{log.module}</span>
                    <span className="text-xs text-gray-500">
                      {new Date(log.timestamp).toLocaleTimeString()}
                    </span>
                  </div>
                  <p className="text-sm text-gray-700 mt-1">{log.message}</p>
                  {log.metadata && (
                    <pre className="text-xs bg-gray-100 p-2 rounded mt-2 overflow-x-auto">
                      {JSON.stringify(log.metadata, null, 2)}
                    </pre>
                  )}
                </div>
              </div>
            ))}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  )
}
```

### Phase 3: Analytics Dashboard
**Duration**: Week 7-8

#### 3.1 Trend Visualization Components
```typescript
// dashboard/components/analytics/TrendChart.tsx
import { Line, Bar } from 'react-chartjs-2'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js'

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend
)

interface TrendChartProps {
  data: any[]
  type: 'line' | 'bar'
  title: string
  timeRange: string
}

export default function TrendChart({ data, type, title, timeRange }: TrendChartProps) {
  const chartData = {
    labels: data.map(item => item.date),
    datasets: [
      {
        label: 'Search Volume',
        data: data.map(item => item.searchVolume),
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.4,
      },
      {
        label: 'Growth Rate',
        data: data.map(item => item.growthRate),
        borderColor: 'rgb(16, 185, 129)',
        backgroundColor: 'rgba(16, 185, 129, 0.1)',
        tension: 0.4,
      }
    ],
  }

  const options = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: title,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  }

  const ChartComponent = type === 'line' ? Line : Bar

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <ChartComponent data={chartData} options={options} />
    </div>
  )
}
```

#### 3.2 Tag Cloud Component
```typescript
// dashboard/components/analytics/TagCloud.tsx
import { useMemo } from 'react'
import { TagCloud as ReactTagCloud } from 'react-tagcloud'

interface TagCloudProps {
  trends: Array<{
    keyword: string
    searchVolume: number
    category: string
  }>
}

export default function TagCloud({ trends }: TagCloudProps) {
  const tagData = useMemo(() => {
    return trends.map(trend => ({
      value: trend.keyword,
      count: trend.searchVolume,
      category: trend.category
    }))
  }, [trends])

  const customRenderer = (tag: any, size: number, color: string) => (
    <span
      key={tag.value}
      style={{
        fontSize: `${size}px`,
        color: color,
        margin: '3px',
        padding: '3px 6px',
        display: 'inline-block',
        cursor: 'pointer',
        borderRadius: '4px',
        backgroundColor: `${color}20`,
      }}
      onClick={() => handleTagClick(tag)}
    >
      {tag.value}
    </span>
  )

  const handleTagClick = (tag: any) => {
    // Navigate to trend details or filter
    console.log('Tag clicked:', tag)
  }

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <h3 className="text-lg font-semibold mb-4">Trending Keywords</h3>
      <ReactTagCloud
        minSize={12}
        maxSize={35}
        tags={tagData}
        renderer={customRenderer}
        colorOptions={{
          luminosity: 'bright',
          hue: ['blue', 'green', 'purple', 'orange']
        }}
      />
    </div>
  )
}
```

#### 3.3 Performance Metrics Dashboard
```typescript
// dashboard/components/analytics/PerformanceMetrics.tsx
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { TrendingUp, TrendingDown, Clock, Globe } from 'lucide-react'

interface MetricsProps {
  metrics: {
    totalTrends: number
    activeTrends: number
    avgResponseTime: number
    uptime: number
    deploymentSuccess: number
    cacheHitRatio: number
  }
}

export default function PerformanceMetrics({ metrics }: MetricsProps) {
  const metricCards = [
    {
      title: 'Total Trends',
      value: metrics.totalTrends,
      change: '+12%',
      icon: TrendingUp,
      color: 'text-green-600'
    },
    {
      title: 'Active Trends',
      value: metrics.activeTrends,
      change: '+5%',
      icon: Globe,
      color: 'text-blue-600'
    },
    {
      title: 'Avg Response Time',
      value: `${metrics.avgResponseTime}ms`,
      change: '-8%',
      icon: Clock,
      color: 'text-purple-600'
    },
    {
      title: 'System Uptime',
      value: `${metrics.uptime}%`,
      change: '+0.1%',
      icon: TrendingUp,
      color: 'text-green-600'
    }
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {metricCards.map((metric) => (
        <Card key={metric.title}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {metric.title}
            </CardTitle>
            <metric.icon className={`h-4 w-4 ${metric.color}`} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metric.value}</div>
            <p className="text-xs text-muted-foreground">
              {metric.change} from last month
            </p>
          </CardContent>
        </Card>
      ))}
      
      <Card className="md:col-span-2">
        <CardHeader>
          <CardTitle>System Health</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <div className="flex justify-between text-sm">
              <span>Deployment Success Rate</span>
              <span>{metrics.deploymentSuccess}%</span>
            </div>
            <Progress value={metrics.deploymentSuccess} className="mt-2" />
          </div>
          <div>
            <div className="flex justify-between text-sm">
              <span>Cache Hit Ratio</span>
              <span>{metrics.cacheHitRatio}%</span>
            </div>
            <Progress value={metrics.cacheHitRatio} className="mt-2" />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
```

## Key Components

### Directory Structure
```
dashboard/
├── components/
│   ├── layout/
│   │   ├── DashboardLayout.tsx
│   │   ├── Sidebar.tsx
│   │   └── Header.tsx
│   ├── controls/
│   │   ├── SystemControls.tsx
│   │   ├── OperationLogs.tsx
│   │   └── ConfigManager.tsx
│   ├── analytics/
│   │   ├── TrendChart.tsx
│   │   ├── TagCloud.tsx
│   │   ├── PerformanceMetrics.tsx
│   │   └── AnalyticsDashboard.tsx
│   ├── trends/
│   │   ├── TrendList.tsx
│   │   ├── TrendDetails.tsx
│   │   └── TrendFilters.tsx
│   └── ui/
│       ├── button.tsx
│       ├── card.tsx
│       ├── badge.tsx
│       └── progress.tsx
├── pages/
│   ├── api/
│   │   ├── auth/
│   │   ├── trends/
│   │   ├── analytics/
│   │   └── controls/
│   ├── control-panel.tsx
│   ├── analytics.tsx
│   ├── trends.tsx
│   └── settings.tsx
├── hooks/
│   ├── useSystemStatus.ts
│   ├── useTriggerFunction.ts
│   ├── useWebSocket.ts
│   └── useAnalytics.ts
├── lib/
│   ├── auth.ts
│   ├── api.ts
│   ├── websocket.ts
│   └── utils.ts
└── styles/
    └── globals.css
```

### Configuration
```typescript
// dashboard/lib/config.ts
export const DASHBOARD_CONFIG = {
  auth: {
    supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL!,
    supabaseAnonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    sessionTimeout: 24 * 60 * 60 * 1000, // 24 hours
  },
  api: {
    baseUrl: process.env.NEXT_PUBLIC_API_BASE_URL!,
    timeout: 30000,
    retries: 3,
  },
  websocket: {
    url: process.env.NEXT_PUBLIC_WS_URL!,
    reconnectInterval: 5000,
    maxReconnectAttempts: 10,
  },
  features: {
    realTimeLogs: true,
    analyticsRefreshInterval: 60000, // 1 minute
    autoRefreshDashboard: true,
  }
}
```

## API Endpoints

### Dashboard API Routes
```typescript
// dashboard/pages/api/controls/trigger.ts
import { NextApiRequest, NextApiResponse } from 'next'
import { getServerSession } from 'next-auth'
import { authOptions, requireAuth } from '@/lib/auth'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const session = await getServerSession(req, res, authOptions)
  
  if (!session || !hasPermission(session.user, 'trigger_functions')) {
    return res.status(403).json({ error: 'Forbidden' })
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  const { functionName, parameters } = req.body

  try {
    // Trigger the specified function via Celery
    const result = await triggerCeleryTask(functionName, parameters)
    
    res.status(200).json({
      success: true,
      taskId: result.taskId,
      message: `${functionName} triggered successfully`
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    })
  }
}
```

## Testing Strategy

### Component Tests
```typescript
// dashboard/__tests__/components/SystemControls.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { SessionProvider } from 'next-auth/react'
import SystemControls from '@/components/controls/SystemControls'

const mockSession = {
  user: { id: '1', role: 'admin', permissions: ['trigger_functions'] }
}

describe('SystemControls', () => {
  it('renders control sections', () => {
    render(
      <SessionProvider session={mockSession}>
        <SystemControls />
      </SessionProvider>
    )
    
    expect(screen.getByText('Data Collection')).toBeInTheDocument()
    expect(screen.getByText('Content Generation')).toBeInTheDocument()
    expect(screen.getByText('Deployment')).toBeInTheDocument()
  })

  it('triggers function when button clicked', async () => {
    const mockTrigger = jest.fn()
    
    render(
      <SessionProvider session={mockSession}>
        <SystemControls triggerFunction={mockTrigger} />
      </SessionProvider>
    )
    
    fireEvent.click(screen.getByText('Trigger'))
    
    await waitFor(() => {
      expect(mockTrigger).toHaveBeenCalledWith('scrape-trends')
    })
  })
})
```

## Deployment Notes

### Environment Variables
```bash
# Authentication
NEXTAUTH_URL=https://dashboard.yourdomain.com
NEXTAUTH_SECRET=your_nextauth_secret
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# API Configuration
NEXT_PUBLIC_API_BASE_URL=https://api.yourdomain.com
NEXT_PUBLIC_WS_URL=wss://api.yourdomain.com/ws

# Feature Flags
NEXT_PUBLIC_ENABLE_REAL_TIME_LOGS=true
NEXT_PUBLIC_ANALYTICS_REFRESH_INTERVAL=60000
```

### Build Configuration
```json
// dashboard/package.json
{
  "name": "trend-dashboard",
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "test": "jest",
    "test:watch": "jest --watch"
  },
  "dependencies": {
    "next": "^14.0.0",
    "react": "^18.0.0",
    "next-auth": "^4.24.0",
    "@supabase/supabase-js": "^2.38.0",
    "chart.js": "^4.4.0",
    "react-chartjs-2": "^5.2.0",
    "react-tagcloud": "^2.3.0",
    "tailwindcss": "^3.3.0"
  }
}
```

## Success Criteria
- Sub-second page load times for all dashboard views
- Real-time updates with <1 second latency
- 100% uptime for dashboard availability
- Comprehensive control over all system functions
- Intuitive analytics visualization and reporting
