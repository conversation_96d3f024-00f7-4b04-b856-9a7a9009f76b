# Hybrid Google Trends Content Platform - Product Requirements Document

## 1. System Architecture Overview

The platform consists of 10 interconnected modules that form a complete content automation pipeline from trend detection to live deployment.

### 1.1 Data Flow Pipeline
```
Google Trends → Scraper → Content Generator → Static Site → Deploy → Live Page → DNS Redirect → Cleanup
```

### 1.2 Technology Stack Summary
- **Backend**: Python (FastAPI), Celery, Redis
- **Frontend**: Next.js, TypeScript, Tailwind CSS
- **Database**: Supabase (PostgreSQL)
- **Infrastructure**: OCI Ampere A1, Coolify, Cloudflare
- **AI Services**: OpenAI-compatible endpoints (text + image generation)

## 2. Module Implementation Specifications

### 2.1 Trend Scraper Module

**Phase 1: Core Scraping**
- Set up pytrends library with proxy rotation
- Implement trends24.in scraping for Twitter trends
- Build modular scrapers for multiple sources
- Configure regional filtering (US, UK, CA, AU)
- Configure category filters (Technology, Health, Entertainment)
- Create keyword scoring algorithm (search volume + growth rate)
- Build data validation pipeline

**Phase 2: Advanced Features**
- Multi-source integration (Google Trends + trends24.in + custom scrapers)
- Duplicate detection using fuzzy matching
- Rate limiting with exponential backoff
- Error handling and retry mechanisms

**Modular Design:**
- `scraper/sources/` - Individual scraper modules
  - `google_trends.py` - Google Trends scraper
  - `trends24_scraper.py` - trends24.in scraper
  - `base_scraper.py` - Abstract base class for all scrapers
- `scraper/core.py` - Main orchestration logic
- `scraper/filters.py` - Reusable filtering components
- `scraper/validators.py` - Data validation rules

**Deliverables:**
- Modular scraper architecture with pluggable sources
- Configuration files for API keys and thresholds
- Unit tests for each scraper module

### 2.2 Site Generator Module

**Phase 1: Template System**
- Design MDX template structure
- Implement Jinja2 template engine
- Create frontmatter schema validation
- Build slug generation with conflict resolution

**Phase 2: AI Content Generation**
- OpenAI-compatible API integration for text generation
- OpenAI-compatible API integration for image generation
- Conditional code snippet generation for tech topics
- Content moderation pipeline
- Image optimization (WebP conversion, compression)

**Phase 3: Static File Management**
- Git repository integration
- File writing and asset management
- Commit automation with structured messages
- Build trigger system

**Modular Design:**
- `generator/ai/` - AI service modules
  - `text_generator.py` - Text generation with configurable endpoints
  - `image_generator.py` - Image generation with configurable endpoints
  - `base_ai_client.py` - Abstract base for AI services
- `generator/templates/` - Template management
- `generator/assets.py` - Reusable asset management
- `generator/git_ops.py` - Git operations module

**Deliverables:**
- Modular AI service architecture supporting multiple providers
- Template system with inheritance and composition
- Asset management with optimization pipeline

### 2.3 Coolify Deploy Orchestrator

**Phase 1: Infrastructure Setup**
- Provision OCI Ampere A1 VM (4 vCPU, 24GB RAM)
- Install Coolify via Docker Compose
- Configure SSL certificates
- Set up monitoring and logging

**Phase 2: Deploy Pipeline**
- Connect Git repository to Coolify
- Configure Next.js build settings
- Implement webhook endpoint for deployments
- Set up Incremental Static Regeneration (ISR)

**Modular Design:**
- `deploy/orchestrator.py` - Main deployment controller
- `deploy/providers/` - Deployment provider modules
  - `coolify_provider.py` - Coolify-specific implementation
  - `base_provider.py` - Abstract deployment provider
- `deploy/hooks.py` - Webhook management
- `deploy/monitoring.py` - Deployment monitoring

**Deliverables:**
- Pluggable deployment architecture
- Infrastructure provisioning automation
- Deployment status tracking system

### 2.4 DNS & Redirect Layer

**Phase 1: Cloudflare Worker (Week 3-4)**
- Implement wildcard CNAME redirect logic
- Add error handling for malformed domains
- Configure caching headers (TTL: 1 hour)
- Set up monitoring for redirect performance

**Phase 2: DNS Automation (Week 4)**
- Cloudflare API integration for CNAME management
- Automated DNS record creation/deletion
- Zone file backup and recovery

**Deliverables:**
- `worker/redirect.js` - Cloudflare Worker script
- `dns/cloudflare_api.py` - DNS management functions
- Configuration for Cloudflare zones

### 2.5 Dashboard & Control Module

**Phase 1: Authentication**
- Supabase Auth integration
- JWT token management
- Role-based access control (Admin, Editor, Viewer)
- Session management and logout

**Phase 2: Control Panel Dashboard**
- Individual function triggers (scrape, generate, deploy, cleanup)
- System status monitoring
- Manual intervention controls
- Configuration management interface
- Real-time operation logs

**Phase 3: Main Analytics Dashboard**
- Trend visualization with interactive charts
- Tag cloud for trending keywords
- Trend selection and filtering interface
- Analytics data aggregation and display
- Historical trend analysis
- Performance metrics visualization

**Modular Design:**
- `dashboard/components/` - Reusable UI components
  - `controls/` - Control panel components (buttons, forms, triggers)
  - `analytics/` - Analytics visualization components
  - `charts/` - Chart components (tag cloud, line charts, heatmaps)
- `dashboard/pages/` - Page-level components
  - `control-panel.tsx` - Function control interface
  - `analytics.tsx` - Main analytics dashboard
  - `trends.tsx` - Trend management interface
- `dashboard/api/` - API route handlers
- `dashboard/hooks/` - Custom React hooks for data fetching
- `dashboard/utils/` - Utility functions and helpers

**Deliverables:**
- Control panel for individual function execution
- Analytics dashboard with trend visualization
- Modular component library for dashboard features

### 2.6 Database & Storage Module

**Phase 1: Schema Design**
- Design PostgreSQL schema for trends, content, deployments
- Implement Row Level Security (RLS) policies
- Create database migrations
- Set up backup and recovery procedures

**Phase 2: Data Models**
- Supabase client configuration
- CRUD operations for all entities
- Data validation and constraints
- Indexing strategy for performance

**Modular Design:**
- `database/models/` - Data model definitions
  - `trend_model.py` - Trend entity operations
  - `content_model.py` - Content entity operations
  - `deployment_model.py` - Deployment tracking
  - `base_model.py` - Abstract base model with common operations
- `database/migrations/` - Database migration files
- `database/repositories/` - Repository pattern implementation
- `database/validators/` - Data validation modules

**Schema:**
```sql
-- trends table
CREATE TABLE trends (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  keyword TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  status TEXT CHECK (status IN ('pending', 'approved', 'rejected', 'live', 'expired')),
  region TEXT NOT NULL,
  category TEXT NOT NULL,
  search_volume INTEGER,
  growth_rate DECIMAL,
  created_at TIMESTAMP DEFAULT NOW(),
  expire_at TIMESTAMP,
  deployed_at TIMESTAMP,
  ads_enabled BOOLEAN DEFAULT true
);

-- content table
CREATE TABLE content (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  trend_id UUID REFERENCES trends(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  body TEXT NOT NULL,
  meta_tags JSONB,
  hero_image_url TEXT,
  code_snippet TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);

-- deployments table
CREATE TABLE deployments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  trend_id UUID REFERENCES trends(id),
  status TEXT CHECK (status IN ('pending', 'building', 'success', 'failed')),
  build_log TEXT,
  deploy_url TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);
```

**Deliverables:**
- Modular data access layer with repository pattern
- Database schema with proper relationships and constraints
- Migration system for schema updates

### 2.7 House-keeping Module

**Phase 1: Cleanup Jobs**
- Implement content expiration logic (14-day TTL)
- Database record cleanup
- File system cleanup (remove static files)
- DNS record deletion
- CDN cache purging

**Phase 2: Optimization**
- Database maintenance (VACUUM, ANALYZE)
- Log rotation and archival
- Performance monitoring and alerting

**Modular Design:**
- `housekeeping/tasks/` - Individual cleanup tasks
  - `content_cleanup.py` - Content expiration handling
  - `dns_cleanup.py` - DNS record management
  - `cache_cleanup.py` - CDN cache purging
  - `db_maintenance.py` - Database optimization
- `housekeeping/scheduler.py` - Task scheduling and orchestration
- `housekeeping/config.py` - Cleanup policies and thresholds

**Deliverables:**
- Modular cleanup task system
- Configurable cleanup policies
- Monitoring and alerting for cleanup operations

### 2.8 Monitoring & Observability

**Phase 1: Logging**
- Structured logging with JSON format
- Log aggregation with Loki
- Grafana dashboard configuration
- Alert rules for critical errors

**Phase 2: Metrics**
- Application performance monitoring
- Infrastructure monitoring (CPU, RAM, disk)
- Cost tracking and alerts
- SLA monitoring (uptime, response time)

**Modular Design:**
- `monitoring/loggers/` - Logging modules
  - `structured_logger.py` - JSON logging formatter
  - `log_aggregator.py` - Log collection and forwarding
- `monitoring/metrics/` - Metrics collection
  - `app_metrics.py` - Application-level metrics
  - `infra_metrics.py` - Infrastructure metrics
- `monitoring/dashboards/` - Dashboard configurations
- `monitoring/alerts/` - Alert rule definitions

**Deliverables:**
- Centralized logging system
- Comprehensive monitoring dashboards
- Automated alerting system

### 2.9 Security & Compliance

**Phase 1: Secret Management (Week 8-9)**
- HashiCorp Vault setup and configuration
- API key rotation automation
- Secure credential storage
- Environment-specific configurations

**Phase 2: Security Hardening (Week 10)**
- Content Security Policy (CSP) implementation
- Rate limiting on all endpoints
- Input validation and sanitization
- HTTPS enforcement
- Security headers configuration

**Deliverables:**
- Vault configuration files
- Security middleware
- Rate limiting configurations

### 2.10 Infrastructure as Code

**Phase 1: Terraform Setup (Week 1-2)**
- OCI provider configuration
- VM provisioning scripts
- Network security groups
- Storage configuration

**Phase 2: Automation (Week 10-11)**
- Automated deployment pipeline
- Environment management (dev, staging, prod)
- Disaster recovery procedures
- Cost optimization strategies

**Deliverables:**
- `terraform/` - Infrastructure definitions
- `scripts/` - Deployment automation
- Documentation for infrastructure management

## 3. Scheduled Jobs Configuration

### 3.1 Celery Beat Schedule
```python
CELERY_BEAT_SCHEDULE = {
    'scrape-trends': {
        'task': 'scraper.tasks.scrape_trends',
        'schedule': crontab(minute='*/15'),  # Every 15 minutes
    },
    'cleanup-expired': {
        'task': 'housekeeping.tasks.cleanup_expired_content',
        'schedule': crontab(hour=2, minute=0),  # Daily at 02:00 UTC
    },
    'sync-analytics': {
        'task': 'analytics.tasks.sync_metrics',
        'schedule': crontab(minute=0),  # Hourly
    },
    'rotate-api-keys': {
        'task': 'security.tasks.rotate_keys',
        'schedule': crontab(day_of_month=1, hour=3, minute=0),  # Monthly
    }
}
```

## 4. API Specifications

### 4.1 Core Endpoints
- `GET /api/trends` - List trends with pagination and filtering
- `POST /api/trends/{id}/approve` - Approve pending trend
- `POST /api/trends/{id}/reject` - Reject pending trend
- `POST /api/deploy/trigger` - Manual deployment trigger
- `GET /api/health` - System health check
- `POST /api/dns/create` - Create DNS record
- `DELETE /api/dns/{record_id}` - Delete DNS record

### 4.2 Authentication
- JWT-based authentication for dashboard
- API key authentication for internal services
- Role-based access control with permissions

## 5. Testing Strategy

### 5.1 Unit Testing
- Python modules: pytest with 80%+ coverage
- JavaScript/TypeScript: Jest with React Testing Library
- Database operations: PostgreSQL test database

### 5.2 Integration Testing
- API endpoint testing with FastAPI TestClient
- End-to-end pipeline testing
- Deployment pipeline validation

### 5.3 Performance Testing
- Load testing with Locust
- Database performance testing
- CDN and caching validation

## 6. Deployment Pipeline

### 6.1 Development Phase
1. Set up development environment
2. Implement core modules (Scraper, Generator)
3. Basic database and API setup
4. Local testing and validation

### 6.2 Integration Phase
1. Deploy infrastructure (OCI VM, Coolify)
2. Integrate all modules
3. Implement dashboard and authentication
4. End-to-end testing

### 6.3 Production Phase
1. Security hardening and monitoring
2. Performance optimization
3. Documentation and training
4. Go-live and monitoring

## 7. Success Criteria

### 7.1 Functional Requirements
- Successfully scrape and process 100+ trends daily
- Generate and deploy content within 5 minutes
- Maintain 99.9% uptime for deployed pages
- Clean up expired content within 24 hours

### 7.2 Performance Requirements
- Page load time < 2 seconds
- API response time < 500ms
- Build and deploy time < 3 minutes
- CDN cache hit ratio > 90%

### 7.3 Quality Requirements
- Code coverage > 80%
- Zero critical security vulnerabilities
- Automated monitoring and alerting
- Complete documentation and runbooks